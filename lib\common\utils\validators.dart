/// Utility class for form validation
class Validators {
  // Private constructor to prevent instantiation
  Validators._();

  // ==================== BASIC VALIDATORS ====================

  /// Validate required field
  static String? required(String? value, {String? fieldName}) {
    if (value == null || value.trim().isEmpty) {
      return '${fieldName ?? 'Field'} is required';
    }
    return null;
  }

  /// Validate minimum length
  static String? minLength(String? value, int minLength, {String? fieldName}) {
    if (value == null || value.length < minLength) {
      return '${fieldName ?? 'Field'} must be at least $minLength characters';
    }
    return null;
  }

  /// Validate maximum length
  static String? maxLength(String? value, int maxLength, {String? fieldName}) {
    if (value != null && value.length > maxLength) {
      return '${fieldName ?? 'Field'} must not exceed $maxLength characters';
    }
    return null;
  }

  /// Validate exact length
  static String? exactLength(String? value, int length, {String? fieldName}) {
    if (value == null || value.length != length) {
      return '${fieldName ?? 'Field'} must be exactly $length characters';
    }
    return null;
  }

  // ==================== EMAIL VALIDATORS ====================

  /// Validate email format
  static String? email(String? value, {String? fieldName}) {
    if (value == null || value.isEmpty) return null;
    
    final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
    if (!emailRegex.hasMatch(value)) {
      return '${fieldName ?? 'Email'} format is invalid';
    }
    return null;
  }

  /// Validate required email
  static String? requiredEmail(String? value, {String? fieldName}) {
    final requiredResult = required(value, fieldName: fieldName ?? 'Email');
    if (requiredResult != null) return requiredResult;
    
    return email(value, fieldName: fieldName);
  }

  // ==================== PHONE VALIDATORS ====================

  /// Validate phone number (10 digits)
  static String? phoneNumber(String? value, {String? fieldName}) {
    if (value == null || value.isEmpty) return null;
    
    final digits = value.replaceAll(RegExp(r'\D'), '');
    if (digits.length != 10) {
      return '${fieldName ?? 'Phone number'} must be 10 digits';
    }
    return null;
  }

  /// Validate required phone number
  static String? requiredPhoneNumber(String? value, {String? fieldName}) {
    final requiredResult = required(value, fieldName: fieldName ?? 'Phone number');
    if (requiredResult != null) return requiredResult;
    
    return phoneNumber(value, fieldName: fieldName);
  }

  // ==================== NUMBER VALIDATORS ====================

  /// Validate numeric value
  static String? numeric(String? value, {String? fieldName}) {
    if (value == null || value.isEmpty) return null;
    
    if (double.tryParse(value) == null) {
      return '${fieldName ?? 'Field'} must be a valid number';
    }
    return null;
  }

  /// Validate required numeric value
  static String? requiredNumeric(String? value, {String? fieldName}) {
    final requiredResult = required(value, fieldName: fieldName);
    if (requiredResult != null) return requiredResult;
    
    return numeric(value, fieldName: fieldName);
  }

  /// Validate minimum value
  static String? minValue(String? value, double minValue, {String? fieldName}) {
    if (value == null || value.isEmpty) return null;
    
    final numValue = double.tryParse(value);
    if (numValue == null) {
      return '${fieldName ?? 'Field'} must be a valid number';
    }
    
    if (numValue < minValue) {
      return '${fieldName ?? 'Field'} must be at least $minValue';
    }
    return null;
  }

  /// Validate maximum value
  static String? maxValue(String? value, double maxValue, {String? fieldName}) {
    if (value == null || value.isEmpty) return null;
    
    final numValue = double.tryParse(value);
    if (numValue == null) {
      return '${fieldName ?? 'Field'} must be a valid number';
    }
    
    if (numValue > maxValue) {
      return '${fieldName ?? 'Field'} must not exceed $maxValue';
    }
    return null;
  }

  /// Validate value range
  static String? valueRange(String? value, double minValue, double maxValue, {String? fieldName}) {
    if (value == null || value.isEmpty) return null;
    
    final numValue = double.tryParse(value);
    if (numValue == null) {
      return '${fieldName ?? 'Field'} must be a valid number';
    }
    
    if (numValue < minValue || numValue > maxValue) {
      return '${fieldName ?? 'Field'} must be between $minValue and $maxValue';
    }
    return null;
  }

  // ==================== AMOUNT VALIDATORS ====================

  /// Validate currency amount (positive number with up to 2 decimal places)
  static String? amount(String? value, {String? fieldName}) {
    if (value == null || value.isEmpty) return null;
    
    final numValue = double.tryParse(value);
    if (numValue == null) {
      return '${fieldName ?? 'Amount'} must be a valid number';
    }
    
    if (numValue < 0) {
      return '${fieldName ?? 'Amount'} must be positive';
    }
    
    // Check decimal places
    final parts = value.split('.');
    if (parts.length > 1 && parts[1].length > 2) {
      return '${fieldName ?? 'Amount'} can have at most 2 decimal places';
    }
    
    return null;
  }

  /// Validate required amount
  static String? requiredAmount(String? value, {String? fieldName}) {
    final requiredResult = required(value, fieldName: fieldName ?? 'Amount');
    if (requiredResult != null) return requiredResult;
    
    return amount(value, fieldName: fieldName);
  }

  // ==================== DATE VALIDATORS ====================

  /// Validate date format (DD/MM/YYYY)
  static String? date(String? value, {String? fieldName}) {
    if (value == null || value.isEmpty) return null;
    
    final dateRegex = RegExp(r'^\d{2}/\d{2}/\d{4}$');
    if (!dateRegex.hasMatch(value)) {
      return '${fieldName ?? 'Date'} must be in DD/MM/YYYY format';
    }
    
    try {
      final parts = value.split('/');
      final day = int.parse(parts[0]);
      final month = int.parse(parts[1]);
      final year = int.parse(parts[2]);
      
      final date = DateTime(year, month, day);
      if (date.day != day || date.month != month || date.year != year) {
        return '${fieldName ?? 'Date'} is not a valid date';
      }
    } catch (e) {
      return '${fieldName ?? 'Date'} is not a valid date';
    }
    
    return null;
  }

  /// Validate required date
  static String? requiredDate(String? value, {String? fieldName}) {
    final requiredResult = required(value, fieldName: fieldName ?? 'Date');
    if (requiredResult != null) return requiredResult;
    
    return date(value, fieldName: fieldName);
  }

  /// Validate date is not in future
  static String? pastDate(String? value, {String? fieldName}) {
    final dateResult = date(value, fieldName: fieldName);
    if (dateResult != null) return dateResult;
    
    if (value == null || value.isEmpty) return null;
    
    try {
      final parts = value.split('/');
      final inputDate = DateTime(
        int.parse(parts[2]),
        int.parse(parts[1]),
        int.parse(parts[0]),
      );
      
      if (inputDate.isAfter(DateTime.now())) {
        return '${fieldName ?? 'Date'} cannot be in the future';
      }
    } catch (e) {
      return '${fieldName ?? 'Date'} is not a valid date';
    }
    
    return null;
  }

  // ==================== CLAIM SPECIFIC VALIDATORS ====================

  /// Validate claim number (7 digits)
  static String? claimNumber(String? value, {String? fieldName}) {
    if (value == null || value.isEmpty) return null;
    
    final digits = value.replaceAll(RegExp(r'\D'), '');
    if (digits.length != 7) {
      return '${fieldName ?? 'Claim number'} must be 7 digits';
    }
    return null;
  }

  /// Validate required claim number
  static String? requiredClaimNumber(String? value, {String? fieldName}) {
    final requiredResult = required(value, fieldName: fieldName ?? 'Claim number');
    if (requiredResult != null) return requiredResult;
    
    return claimNumber(value, fieldName: fieldName);
  }

  /// Validate employee number
  static String? employeeNumber(String? value, {String? fieldName}) {
    if (value == null || value.isEmpty) return null;
    
    if (value.length < 3) {
      return '${fieldName ?? 'Employee number'} must be at least 3 characters';
    }
    return null;
  }

  /// Validate required employee number
  static String? requiredEmployeeNumber(String? value, {String? fieldName}) {
    final requiredResult = required(value, fieldName: fieldName ?? 'Employee number');
    if (requiredResult != null) return requiredResult;
    
    return employeeNumber(value, fieldName: fieldName);
  }

  // ==================== PASSWORD VALIDATORS ====================

  /// Validate password strength
  static String? password(String? value, {String? fieldName}) {
    if (value == null || value.isEmpty) return null;
    
    if (value.length < 6) {
      return '${fieldName ?? 'Password'} must be at least 6 characters';
    }
    
    return null;
  }

  /// Validate required password
  static String? requiredPassword(String? value, {String? fieldName}) {
    final requiredResult = required(value, fieldName: fieldName ?? 'Password');
    if (requiredResult != null) return requiredResult;
    
    return password(value, fieldName: fieldName);
  }

  /// Validate password confirmation
  static String? confirmPassword(String? value, String? originalPassword, {String? fieldName}) {
    final requiredResult = required(value, fieldName: fieldName ?? 'Confirm password');
    if (requiredResult != null) return requiredResult;
    
    if (value != originalPassword) {
      return 'Passwords do not match';
    }
    
    return null;
  }

  // ==================== COMPOSITE VALIDATORS ====================

  /// Combine multiple validators
  static String? Function(String?) combine(List<String? Function(String?)> validators) {
    return (String? value) {
      for (final validator in validators) {
        final result = validator(value);
        if (result != null) return result;
      }
      return null;
    };
  }

  /// Create conditional validator
  static String? Function(String?) conditional(
    bool Function() condition,
    String? Function(String?) validator,
  ) {
    return (String? value) {
      if (condition()) {
        return validator(value);
      }
      return null;
    };
  }
}
