import 'dart:io';
import 'package:path/path.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'package:sqlite3_flutter_libs/sqlite3_flutter_libs.dart';
import 'db_schema.dart';

/// Database service for Medical Bills ERP System
/// Handles database initialization, migrations, and connections
class DatabaseService {
  static Database? _database;
  static DatabaseService? _instance;

  DatabaseService._internal();

  static DatabaseService get instance {
    _instance ??= DatabaseService._internal();
    return _instance!;
  }

  /// Get database instance
  Future<Database> get database async {
    _database ??= await _initDatabase();
    return _database!;
  }

  /// Initialize database
  Future<Database> _initDatabase() async {
    // Initialize FFI for desktop platforms
    if (Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
      sqfliteFfiInit();
      databaseFactory = databaseFactoryFfi;
    }

    // Get database path
    final databasesPath = await getDatabasesPath();
    final path = join(databasesPath, DatabaseSchema.databaseName);

    // Open database
    return await openDatabase(
      path,
      version: DatabaseSchema.databaseVersion,
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
      onOpen: _onOpen,
    );
  }

  /// Create database tables
  Future<void> _onCreate(Database db, int version) async {
    // Create all tables
    for (String statement in DatabaseSchema.createTableStatements) {
      await db.execute(statement);
    }

    // Insert initial data
    for (String statement in DatabaseSchema.initialDataStatements) {
      await db.execute(statement);
    }

    print('Database created successfully with version $version');
  }

  /// Handle database upgrades
  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    print('Upgrading database from version $oldVersion to $newVersion');
    
    // Add migration logic here when needed
    // For now, we'll recreate tables (development only)
    if (oldVersion < newVersion) {
      // Drop existing tables
      await db.execute('DROP TABLE IF EXISTS ${DatabaseSchema.claimStatusHistoryTable}');
      await db.execute('DROP TABLE IF EXISTS ${DatabaseSchema.consultationRegisterTable}');
      await db.execute('DROP TABLE IF EXISTS ${DatabaseSchema.doctorsRegisterTable}');
      await db.execute('DROP TABLE IF EXISTS ${DatabaseSchema.medicalClaimRegisterTable}');
      await db.execute('DROP TABLE IF EXISTS ${DatabaseSchema.claimMasterTable}');
      await db.execute('DROP TABLE IF EXISTS ${DatabaseSchema.usersTable}');
      
      // Recreate tables
      await _onCreate(db, newVersion);
    }
  }

  /// Handle database open
  Future<void> _onOpen(Database db) async {
    // Enable foreign key constraints
    await db.execute('PRAGMA foreign_keys = ON');
    print('Database opened successfully');
  }

  /// Close database connection
  Future<void> close() async {
    if (_database != null) {
      await _database!.close();
      _database = null;
    }
  }

  /// Execute raw SQL query
  Future<List<Map<String, dynamic>>> rawQuery(String sql, [List<dynamic>? arguments]) async {
    final db = await database;
    return await db.rawQuery(sql, arguments);
  }

  /// Execute raw SQL insert/update/delete
  Future<int> rawExecute(String sql, [List<dynamic>? arguments]) async {
    final db = await database;
    return await db.rawUpdate(sql, arguments);
  }

  /// Insert data into table
  Future<int> insert(String table, Map<String, dynamic> data) async {
    final db = await database;
    return await db.insert(table, data, conflictAlgorithm: ConflictAlgorithm.replace);
  }

  /// Update data in table
  Future<int> update(String table, Map<String, dynamic> data, String where, List<dynamic> whereArgs) async {
    final db = await database;
    return await db.update(table, data, where: where, whereArgs: whereArgs);
  }

  /// Delete data from table
  Future<int> delete(String table, String where, List<dynamic> whereArgs) async {
    final db = await database;
    return await db.delete(table, where: where, whereArgs: whereArgs);
  }

  /// Query data from table
  Future<List<Map<String, dynamic>>> query(
    String table, {
    List<String>? columns,
    String? where,
    List<dynamic>? whereArgs,
    String? orderBy,
    int? limit,
    int? offset,
  }) async {
    final db = await database;
    return await db.query(
      table,
      columns: columns,
      where: where,
      whereArgs: whereArgs,
      orderBy: orderBy,
      limit: limit,
      offset: offset,
    );
  }

  /// Get database path for debugging
  Future<String> getDatabasePath() async {
    final databasesPath = await getDatabasesPath();
    return join(databasesPath, DatabaseSchema.databaseName);
  }

  /// Check if database exists
  Future<bool> databaseExists() async {
    final path = await getDatabasePath();
    return File(path).exists();
  }

  /// Delete database file (for testing/reset)
  Future<void> deleteDatabase() async {
    final path = await getDatabasePath();
    if (await File(path).exists()) {
      await File(path).delete();
      _database = null;
    }
  }
}
