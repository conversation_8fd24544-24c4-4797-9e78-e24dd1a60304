import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../common/models/user_model.dart';
import '../../../database/db_helper.dart';

part 'auth_repository.g.dart';

/// Authentication repository for handling user authentication
class AuthRepository {
  /// Login user with credentials
  Future<UserModel?> login(String userId, String password) async {
    try {
      // Authenticate user using database helper
      final userData = await DatabaseHelper.authenticateUser(userId, password);
      
      if (userData != null) {
        return UserModel.fromJson(userData);
      }
      
      return null;
    } catch (e) {
      throw Exception('Login failed: ${e.toString()}');
    }
  }

  /// Logout user (clear session data)
  Future<void> logout() async {
    // In a real app, you might want to:
    // - Clear stored tokens
    // - Clear cached user data
    // - Notify server of logout
    // For now, we just return
    return;
  }

  /// Get user by ID
  Future<UserModel?> getUserById(String userId) async {
    try {
      final userData = await DatabaseHelper.getUserById(userId);
      
      if (userData != null) {
        return UserModel.fromJson(userData);
      }
      
      return null;
    } catch (e) {
      throw Exception('Failed to get user: ${e.toString()}');
    }
  }

  /// Get all users (admin only)
  Future<List<UserModel>> getAllUsers() async {
    try {
      final usersData = await DatabaseHelper.getAllUsers();
      
      return usersData.map((userData) => UserModel.fromJson(userData)).toList();
    } catch (e) {
      throw Exception('Failed to get users: ${e.toString()}');
    }
  }

  /// Create new user (admin only)
  Future<void> createUser(UserModel user) async {
    try {
      await DatabaseHelper.saveUser(user.toJson());
    } catch (e) {
      throw Exception('Failed to create user: ${e.toString()}');
    }
  }

  /// Update user (admin only)
  Future<void> updateUser(UserModel user) async {
    try {
      await DatabaseHelper.saveUser(user.copyWithUpdatedTimestamp().toJson());
    } catch (e) {
      throw Exception('Failed to update user: ${e.toString()}');
    }
  }

  /// Delete user (admin only)
  Future<void> deleteUser(String userId) async {
    try {
      await DatabaseHelper.deleteUser(userId);
    } catch (e) {
      throw Exception('Failed to delete user: ${e.toString()}');
    }
  }

  /// Change user password
  Future<void> changePassword(String userId, String oldPassword, String newPassword) async {
    try {
      // First verify old password
      final user = await login(userId, oldPassword);
      if (user == null) {
        throw Exception('Current password is incorrect');
      }

      // Update password
      final updatedUser = user.copyWith(
        password: newPassword,
        updatedAt: DateTime.now(),
      );
      
      await updateUser(updatedUser);
    } catch (e) {
      throw Exception('Failed to change password: ${e.toString()}');
    }
  }

  /// Check if user ID is available
  Future<bool> isUserIdAvailable(String userId) async {
    try {
      final user = await getUserById(userId);
      return user == null;
    } catch (e) {
      // If there's an error getting the user, assume it's available
      return true;
    }
  }

  /// Validate user credentials format
  bool validateCredentials(String userId, String password) {
    // Basic validation
    if (userId.trim().isEmpty || password.trim().isEmpty) {
      return false;
    }

    if (userId.length < 3) {
      return false;
    }

    if (password.length < 6) {
      return false;
    }

    return true;
  }

  /// Get user statistics (admin only)
  Future<Map<String, int>> getUserStatistics() async {
    try {
      final users = await getAllUsers();
      
      final stats = <String, int>{};
      
      // Count users by role
      for (final user in users) {
        final roleKey = user.role.displayName;
        stats[roleKey] = (stats[roleKey] ?? 0) + 1;
      }
      
      // Add total count
      stats['Total'] = users.length;
      
      return stats;
    } catch (e) {
      throw Exception('Failed to get user statistics: ${e.toString()}');
    }
  }

  /// Search users by name or user ID
  Future<List<UserModel>> searchUsers(String searchTerm) async {
    try {
      final allUsers = await getAllUsers();
      
      if (searchTerm.trim().isEmpty) {
        return allUsers;
      }
      
      final lowerSearchTerm = searchTerm.toLowerCase();
      
      return allUsers.where((user) {
        return user.userId.toLowerCase().contains(lowerSearchTerm) ||
               (user.name?.toLowerCase().contains(lowerSearchTerm) ?? false) ||
               (user.email?.toLowerCase().contains(lowerSearchTerm) ?? false);
      }).toList();
    } catch (e) {
      throw Exception('Failed to search users: ${e.toString()}');
    }
  }

  /// Reset user password (admin only)
  Future<void> resetUserPassword(String userId, String newPassword) async {
    try {
      final user = await getUserById(userId);
      if (user == null) {
        throw Exception('User not found');
      }

      final updatedUser = user.copyWith(
        password: newPassword,
        updatedAt: DateTime.now(),
      );
      
      await updateUser(updatedUser);
    } catch (e) {
      throw Exception('Failed to reset password: ${e.toString()}');
    }
  }

  /// Get users by role
  Future<List<UserModel>> getUsersByRole(String role) async {
    try {
      final allUsers = await getAllUsers();
      
      return allUsers.where((user) => user.role.code == role).toList();
    } catch (e) {
      throw Exception('Failed to get users by role: ${e.toString()}');
    }
  }

  /// Check if user has permission for action
  bool hasPermission(UserModel user, String action) {
    switch (action) {
      case 'admin':
        return user.canAccessAdmin;
      case 'forward_claims':
        return user.canForwardClaims;
      case 'approve_claims':
        return user.canApproveClaims;
      case 'manage_users':
        return user.canAccessAdmin;
      case 'manage_doctors':
        return user.canAccessAdmin;
      case 'view_reports':
        return true; // All users can view reports
      case 'export_data':
        return user.canAccessAdmin || user.canApproveClaims;
      default:
        return false;
    }
  }
}

/// Provider for auth repository
@riverpod
AuthRepository authRepository(AuthRepositoryRef ref) {
  return AuthRepository();
}
