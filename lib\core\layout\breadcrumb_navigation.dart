import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../theme/app_theme.dart';
import 'app_shell.dart';

/// Professional breadcrumb navigation for ERP system
class BreadcrumbNavigation extends StatelessWidget {
  final String currentPath;

  const BreadcrumbNavigation({
    super.key,
    required this.currentPath,
  });

  @override
  Widget build(BuildContext context) {
    final breadcrumbs = NavigationConfig.getBreadcrumbs(currentPath);
    
    if (breadcrumbs.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      height: 50,
      padding: const EdgeInsets.symmetric(horizontal: 24),
      decoration: BoxDecoration(
        color: AppTheme.backgroundColor,
        border: Border(
          bottom: BorderSide(
            color: AppTheme.borderColor,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.home_outlined,
            size: 16,
            color: AppTheme.textSecondary,
          ),
          const SizedBox(width: 8),
          
          // Home link
          InkWell(
            onTap: () => context.go('/dashboard'),
            child: Text(
              'Home',
              style: TextStyle(
                color: AppTheme.primaryColor,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          
          // Breadcrumb items
          ...breadcrumbs.asMap().entries.map((entry) {
            final index = entry.key;
            final breadcrumb = entry.value;
            final isLast = index == breadcrumbs.length - 1;
            
            return Row(
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                  child: Icon(
                    Icons.chevron_right,
                    size: 16,
                    color: AppTheme.textSecondary,
                  ),
                ),
                Text(
                  breadcrumb,
                  style: TextStyle(
                    color: isLast ? AppTheme.textPrimary : AppTheme.primaryColor,
                    fontSize: 14,
                    fontWeight: isLast ? FontWeight.w600 : FontWeight.w500,
                  ),
                ),
              ],
            );
          }).toList(),
          
          const Spacer(),
          
          // Page actions
          _buildPageActions(context),
        ],
      ),
    );
  }

  Widget _buildPageActions(BuildContext context) {
    // Show different actions based on current page
    switch (currentPath) {
      case '/claims':
        return Row(
          children: [
            _buildActionButton(
              icon: Icons.add,
              label: 'New Claim',
              onPressed: () => context.go('/claims/new'),
            ),
            const SizedBox(width: 8),
            _buildActionButton(
              icon: Icons.upload,
              label: 'Upload',
              onPressed: () => context.go('/claims/upload'),
            ),
            const SizedBox(width: 8),
            _buildActionButton(
              icon: Icons.download,
              label: 'Export',
              onPressed: () {
                // TODO: Export claims
              },
            ),
          ],
        );
      case '/reports':
        return Row(
          children: [
            _buildActionButton(
              icon: Icons.refresh,
              label: 'Refresh',
              onPressed: () {
                // TODO: Refresh reports
              },
            ),
            const SizedBox(width: 8),
            _buildActionButton(
              icon: Icons.download,
              label: 'Export',
              onPressed: () {
                // TODO: Export reports
              },
            ),
          ],
        );
      case '/admin/users':
        return Row(
          children: [
            _buildActionButton(
              icon: Icons.person_add,
              label: 'Add User',
              onPressed: () {
                // TODO: Add user
              },
            ),
          ],
        );
      default:
        return const SizedBox.shrink();
    }
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
  }) {
    return Container(
      height: 32,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: AppTheme.borderColor),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(6),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 12),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  icon,
                  size: 16,
                  color: AppTheme.primaryColor,
                ),
                const SizedBox(width: 6),
                Text(
                  label,
                  style: TextStyle(
                    color: AppTheme.primaryColor,
                    fontSize: 13,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
