import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import 'theme/app_theme.dart';
import 'router/app_router.dart';
import '../database/db_service.dart';

/// Main application widget
class MedicalBillsApp extends ConsumerStatefulWidget {
  const MedicalBillsApp({super.key});

  @override
  ConsumerState<MedicalBillsApp> createState() => _MedicalBillsAppState();
}

class _MedicalBillsAppState extends ConsumerState<MedicalBillsApp> {
  late final GoRouter _router;
  bool _isInitialized = false;
  String? _initError;

  @override
  void initState() {
    super.initState();
    _initializeApp();
  }

  /// Initialize the application
  Future<void> _initializeApp() async {
    try {
      // Initialize database
      await DatabaseService.instance.database;
      
      // Create router
      _router = AppRouter.createRouter(ref);
      
      setState(() {
        _isInitialized = true;
      });
    } catch (e) {
      setState(() {
        _initError = e.toString();
        _isInitialized = true;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!_isInitialized) {
      return MaterialApp(
        title: 'Medical Bills ERP',
        theme: AppTheme.lightTheme,
        home: const SplashScreen(),
        debugShowCheckedModeBanner: false,
      );
    }

    if (_initError != null) {
      return MaterialApp(
        title: 'Medical Bills ERP',
        theme: AppTheme.lightTheme,
        home: ErrorInitScreen(error: _initError!),
        debugShowCheckedModeBanner: false,
      );
    }

    return MaterialApp.router(
      title: 'Medical Bills ERP',
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: ThemeMode.light, // TODO: Make this configurable
      routerConfig: _router,
      debugShowCheckedModeBanner: false,
      builder: (context, child) {
        // Add global error handling and responsive design
        return MediaQuery(
          data: MediaQuery.of(context).copyWith(
            textScaler: TextScaler.linear(
              MediaQuery.of(context).textScaler.scale(1.0).clamp(0.8, 1.2),
            ),
          ),
          child: child ?? const SizedBox.shrink(),
        );
      },
    );
  }

  @override
  void dispose() {
    // Close database connection when app is disposed
    DatabaseService.instance.close();
    super.dispose();
  }
}

/// Splash screen shown during app initialization
class SplashScreen extends StatelessWidget {
  const SplashScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.primaryColor,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // App logo/icon
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.2),
                    blurRadius: 10,
                    offset: const Offset(0, 5),
                  ),
                ],
              ),
              child: const Icon(
                Icons.local_hospital,
                size: 60,
                color: AppTheme.primaryColor,
              ),
            ),
            const SizedBox(height: 32),
            
            // App title
            const Text(
              'Medical Bills ERP',
              style: TextStyle(
                fontSize: 28,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 8),
            
            // Subtitle
            const Text(
              'Amreli District Administration',
              style: TextStyle(
                fontSize: 16,
                color: Colors.white70,
              ),
            ),
            const SizedBox(height: 48),
            
            // Loading indicator
            const CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
            ),
            const SizedBox(height: 16),
            
            // Loading text
            const Text(
              'Initializing...',
              style: TextStyle(
                fontSize: 16,
                color: Colors.white70,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Error screen shown when app initialization fails
class ErrorInitScreen extends StatelessWidget {
  final String error;

  const ErrorInitScreen({super.key, required this.error});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.red.shade50,
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Error icon
              Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  color: Colors.red.shade100,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Icon(
                  Icons.error_outline,
                  size: 60,
                  color: Colors.red.shade600,
                ),
              ),
              const SizedBox(height: 32),
              
              // Error title
              Text(
                'Initialization Failed',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.red.shade800,
                ),
              ),
              const SizedBox(height: 16),
              
              // Error message
              Text(
                'The application failed to initialize properly. Please try restarting the application.',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.red.shade700,
                ),
              ),
              const SizedBox(height: 8),
              
              // Technical error details
              Container(
                padding: const EdgeInsets.all(16),
                margin: const EdgeInsets.symmetric(vertical: 16),
                decoration: BoxDecoration(
                  color: Colors.red.shade100,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.red.shade300),
                ),
                child: Text(
                  'Technical Details:\n$error',
                  style: TextStyle(
                    fontSize: 12,
                    fontFamily: 'monospace',
                    color: Colors.red.shade800,
                  ),
                ),
              ),
              const SizedBox(height: 24),
              
              // Retry button
              ElevatedButton.icon(
                onPressed: () {
                  // Restart the app (this is a simple approach)
                  // In a real app, you might want to implement proper restart logic
                  Navigator.of(context).pushAndRemoveUntil(
                    MaterialPageRoute(builder: (context) => const MedicalBillsApp()),
                    (route) => false,
                  );
                },
                icon: const Icon(Icons.refresh),
                label: const Text('Retry'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red.shade600,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// Global error handler for uncaught exceptions
class AppErrorHandler {
  static void initialize() {
    FlutterError.onError = (FlutterErrorDetails details) {
      // Log error to console in debug mode
      FlutterError.presentError(details);
      
      // TODO: In production, send error to crash reporting service
      // crashlytics.recordFlutterError(details);
    };
  }
}

/// App configuration and constants
class AppConfig {
  static const String appName = 'Medical Bills ERP';
  static const String appVersion = '1.0.0';
  static const String organization = 'Amreli District Administration';
  
  // Database configuration
  static const String databaseName = 'medical_bills.db';
  static const int databaseVersion = 1;
  
  // File upload limits
  static const int maxFileSize = 10 * 1024 * 1024; // 10MB
  static const int maxExcelRows = 10000;
  
  // Pagination
  static const int defaultPageSize = 50;
  static const int maxPageSize = 100;
  
  // Auto-save intervals
  static const Duration autoSaveInterval = Duration(seconds: 30);
  
  // Session timeout
  static const Duration sessionTimeout = Duration(hours: 8);
}
