import 'package:intl/intl.dart';

/// Utility class for formatting various data types
class Formatters {
  // Private constructor to prevent instantiation
  Formatters._();

  // ==================== CURRENCY FORMATTING ====================

  /// Format currency in Indian Rupees
  static String formatCurrency(double? amount, {bool showSymbol = true}) {
    if (amount == null) return '';
    
    final formatter = NumberFormat.currency(
      locale: 'en_IN',
      symbol: showSymbol ? '₹' : '',
      decimalDigits: 2,
    );
    
    return formatter.format(amount);
  }

  /// Format currency without decimal places
  static String formatCurrencyWhole(double? amount, {bool showSymbol = true}) {
    if (amount == null) return '';
    
    final formatter = NumberFormat.currency(
      locale: 'en_IN',
      symbol: showSymbol ? '₹' : '',
      decimalDigits: 0,
    );
    
    return formatter.format(amount);
  }

  /// Format currency in compact form (e.g., ₹1.2L, ₹1.5K)
  static String formatCurrencyCompact(double? amount, {bool showSymbol = true}) {
    if (amount == null) return '';
    
    final formatter = NumberFormat.compactCurrency(
      locale: 'en_IN',
      symbol: showSymbol ? '₹' : '',
    );
    
    return formatter.format(amount);
  }

  // ==================== NUMBER FORMATTING ====================

  /// Format number with Indian number system (lakhs, crores)
  static String formatNumber(double? number, {int decimalPlaces = 0}) {
    if (number == null) return '';
    
    final formatter = NumberFormat('#,##,###.##', 'en_IN');
    return formatter.format(number);
  }

  /// Format percentage
  static String formatPercentage(double? percentage, {int decimalPlaces = 1}) {
    if (percentage == null) return '';
    
    final formatter = NumberFormat.percentPattern('en_IN');
    formatter.minimumFractionDigits = decimalPlaces;
    formatter.maximumFractionDigits = decimalPlaces;
    
    return formatter.format(percentage / 100);
  }

  // ==================== DATE FORMATTING ====================

  /// Format date in DD/MM/YYYY format
  static String formatDate(DateTime? date) {
    if (date == null) return '';
    return DateFormat('dd/MM/yyyy').format(date);
  }

  /// Format date in DD MMM YYYY format (e.g., 15 Jan 2024)
  static String formatDateMedium(DateTime? date) {
    if (date == null) return '';
    return DateFormat('dd MMM yyyy').format(date);
  }

  /// Format date in full format (e.g., Monday, 15 January 2024)
  static String formatDateFull(DateTime? date) {
    if (date == null) return '';
    return DateFormat('EEEE, dd MMMM yyyy').format(date);
  }

  /// Format date and time
  static String formatDateTime(DateTime? dateTime) {
    if (dateTime == null) return '';
    return DateFormat('dd/MM/yyyy HH:mm').format(dateTime);
  }

  /// Format time only
  static String formatTime(DateTime? time) {
    if (time == null) return '';
    return DateFormat('HH:mm').format(time);
  }

  /// Format time with AM/PM
  static String formatTime12Hour(DateTime? time) {
    if (time == null) return '';
    return DateFormat('hh:mm a').format(time);
  }

  /// Format relative time (e.g., "2 hours ago", "yesterday")
  static String formatRelativeTime(DateTime? dateTime) {
    if (dateTime == null) return '';
    
    final now = DateTime.now();
    final difference = now.difference(dateTime);
    
    if (difference.inDays > 365) {
      final years = (difference.inDays / 365).floor();
      return '$years year${years > 1 ? 's' : ''} ago';
    } else if (difference.inDays > 30) {
      final months = (difference.inDays / 30).floor();
      return '$months month${months > 1 ? 's' : ''} ago';
    } else if (difference.inDays > 0) {
      return '${difference.inDays} day${difference.inDays > 1 ? 's' : ''} ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hour${difference.inHours > 1 ? 's' : ''} ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} minute${difference.inMinutes > 1 ? 's' : ''} ago';
    } else {
      return 'Just now';
    }
  }

  // ==================== STRING FORMATTING ====================

  /// Capitalize first letter of each word
  static String capitalizeWords(String? text) {
    if (text == null || text.isEmpty) return '';
    
    return text.split(' ').map((word) {
      if (word.isEmpty) return word;
      return word[0].toUpperCase() + word.substring(1).toLowerCase();
    }).join(' ');
  }

  /// Capitalize first letter only
  static String capitalizeFirst(String? text) {
    if (text == null || text.isEmpty) return '';
    return text[0].toUpperCase() + text.substring(1);
  }

  /// Format phone number
  static String formatPhoneNumber(String? phoneNumber) {
    if (phoneNumber == null || phoneNumber.isEmpty) return '';
    
    // Remove all non-digit characters
    final digits = phoneNumber.replaceAll(RegExp(r'\D'), '');
    
    if (digits.length == 10) {
      // Format as: +91 98765 43210
      return '+91 ${digits.substring(0, 5)} ${digits.substring(5)}';
    } else if (digits.length == 12 && digits.startsWith('91')) {
      // Format as: +91 98765 43210
      return '+${digits.substring(0, 2)} ${digits.substring(2, 7)} ${digits.substring(7)}';
    }
    
    return phoneNumber; // Return original if format not recognized
  }

  /// Truncate text with ellipsis
  static String truncateText(String? text, int maxLength, {String ellipsis = '...'}) {
    if (text == null || text.isEmpty) return '';
    if (text.length <= maxLength) return text;
    
    return text.substring(0, maxLength - ellipsis.length) + ellipsis;
  }

  /// Format file size
  static String formatFileSize(int? bytes) {
    if (bytes == null || bytes <= 0) return '0 B';
    
    const suffixes = ['B', 'KB', 'MB', 'GB', 'TB'];
    var i = 0;
    double size = bytes.toDouble();
    
    while (size >= 1024 && i < suffixes.length - 1) {
      size /= 1024;
      i++;
    }
    
    return '${size.toStringAsFixed(i == 0 ? 0 : 1)} ${suffixes[i]}';
  }

  // ==================== PARSING HELPERS ====================

  /// Parse currency string to double
  static double? parseCurrency(String? currencyString) {
    if (currencyString == null || currencyString.isEmpty) return null;
    
    // Remove currency symbols and commas
    final cleanString = currencyString
        .replaceAll('₹', '')
        .replaceAll(',', '')
        .trim();
    
    return double.tryParse(cleanString);
  }

  /// Parse date string in DD/MM/YYYY format
  static DateTime? parseDate(String? dateString) {
    if (dateString == null || dateString.isEmpty) return null;
    
    try {
      return DateFormat('dd/MM/yyyy').parse(dateString);
    } catch (e) {
      try {
        return DateFormat('yyyy-MM-dd').parse(dateString);
      } catch (e) {
        return null;
      }
    }
  }

  /// Parse phone number to clean digits
  static String? parsePhoneNumber(String? phoneNumber) {
    if (phoneNumber == null || phoneNumber.isEmpty) return null;
    
    final digits = phoneNumber.replaceAll(RegExp(r'\D'), '');
    return digits.isNotEmpty ? digits : null;
  }

  // ==================== VALIDATION HELPERS ====================

  /// Check if string is a valid email
  static bool isValidEmail(String? email) {
    if (email == null || email.isEmpty) return false;
    
    final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
    return emailRegex.hasMatch(email);
  }

  /// Check if string is a valid phone number
  static bool isValidPhoneNumber(String? phoneNumber) {
    if (phoneNumber == null || phoneNumber.isEmpty) return false;
    
    final digits = phoneNumber.replaceAll(RegExp(r'\D'), '');
    return digits.length == 10 || (digits.length == 12 && digits.startsWith('91'));
  }

  /// Check if string is a valid claim number (7 digits)
  static bool isValidClaimNumber(String? claimNumber) {
    if (claimNumber == null || claimNumber.isEmpty) return false;
    
    final digits = claimNumber.replaceAll(RegExp(r'\D'), '');
    return digits.length == 7;
  }
}
