import 'package:flutter/material.dart';

/// Reusable dropdown widget with consistent styling
class AppDropdown<T> extends StatelessWidget {
  final String? label;
  final String? hint;
  final T? value;
  final List<AppDropdownItem<T>> items;
  final Function(T?)? onChanged;
  final String? Function(T?)? validator;
  final bool enabled;
  final bool required;
  final Widget? prefixIcon;

  const AppDropdown({
    super.key,
    this.label,
    this.hint,
    this.value,
    required this.items,
    this.onChanged,
    this.validator,
    this.enabled = true,
    this.required = false,
    this.prefixIcon,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (label != null) ...[
          Row(
            children: [
              Text(
                label!,
                style: theme.textTheme.labelMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
              if (required)
                Text(
                  ' *',
                  style: TextStyle(
                    color: theme.colorScheme.error,
                    fontWeight: FontWeight.bold,
                  ),
                ),
            ],
          ),
          const SizedBox(height: 8),
        ],
        DropdownButtonFormField<T>(
          value: value,
          items: items.map((item) {
            return DropdownMenuItem<T>(
              value: item.value,
              child: Row(
                children: [
                  if (item.icon != null) ...[
                    item.icon!,
                    const SizedBox(width: 8),
                  ],
                  Expanded(
                    child: Text(item.label, overflow: TextOverflow.ellipsis),
                  ),
                ],
              ),
            );
          }).toList(),
          onChanged: enabled ? onChanged : null,
          validator: validator,
          decoration: InputDecoration(
            hintText: hint,
            prefixIcon: prefixIcon,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: theme.colorScheme.outline),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: theme.colorScheme.outline),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color: theme.colorScheme.primary,
                width: 2,
              ),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: theme.colorScheme.error),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: theme.colorScheme.error, width: 2),
            ),
            filled: true,
            fillColor: !enabled
                ? theme.colorScheme.surfaceContainerHighest.withValues(
                    alpha: 0.3,
                  )
                : theme.colorScheme.surface,
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 12,
            ),
          ),
        ),
      ],
    );
  }
}

/// Dropdown item model
class AppDropdownItem<T> {
  final T value;
  final String label;
  final Widget? icon;
  final bool enabled;

  const AppDropdownItem({
    required this.value,
    required this.label,
    this.icon,
    this.enabled = true,
  });
}

/// Specialized dropdown for enums
class AppEnumDropdown<T extends Enum> extends StatelessWidget {
  final String? label;
  final String? hint;
  final T? value;
  final List<T> values;
  final String Function(T) getLabel;
  final Widget? Function(T)? getIcon;
  final Function(T?)? onChanged;
  final String? Function(T?)? validator;
  final bool enabled;
  final bool required;

  const AppEnumDropdown({
    super.key,
    this.label,
    this.hint,
    this.value,
    required this.values,
    required this.getLabel,
    this.getIcon,
    this.onChanged,
    this.validator,
    this.enabled = true,
    this.required = false,
  });

  @override
  Widget build(BuildContext context) {
    final items = values.map((enumValue) {
      return AppDropdownItem<T>(
        value: enumValue,
        label: getLabel(enumValue),
        icon: getIcon?.call(enumValue),
      );
    }).toList();

    return AppDropdown<T>(
      label: label,
      hint: hint,
      value: value,
      items: items,
      onChanged: onChanged,
      validator: validator,
      enabled: enabled,
      required: required,
    );
  }
}

/// Multi-select dropdown widget
class AppMultiSelectDropdown<T> extends StatefulWidget {
  final String? label;
  final String? hint;
  final List<T> selectedValues;
  final List<AppDropdownItem<T>> items;
  final Function(List<T>)? onChanged;
  final String? Function(List<T>?)? validator;
  final bool enabled;
  final bool required;
  final int? maxSelections;

  const AppMultiSelectDropdown({
    super.key,
    this.label,
    this.hint,
    required this.selectedValues,
    required this.items,
    this.onChanged,
    this.validator,
    this.enabled = true,
    this.required = false,
    this.maxSelections,
  });

  @override
  State<AppMultiSelectDropdown<T>> createState() =>
      _AppMultiSelectDropdownState<T>();
}

class _AppMultiSelectDropdownState<T> extends State<AppMultiSelectDropdown<T>> {
  bool _isExpanded = false;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.label != null) ...[
          Row(
            children: [
              Text(
                widget.label!,
                style: theme.textTheme.labelMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
              if (widget.required)
                Text(
                  ' *',
                  style: TextStyle(
                    color: theme.colorScheme.error,
                    fontWeight: FontWeight.bold,
                  ),
                ),
            ],
          ),
          const SizedBox(height: 8),
        ],
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: theme.colorScheme.outline),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            children: [
              InkWell(
                onTap: widget.enabled
                    ? () {
                        setState(() {
                          _isExpanded = !_isExpanded;
                        });
                      }
                    : null,
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        child: widget.selectedValues.isEmpty
                            ? Text(
                                widget.hint ?? 'Select items...',
                                style: theme.textTheme.bodyMedium?.copyWith(
                                  color: theme.colorScheme.onSurfaceVariant,
                                ),
                              )
                            : Wrap(
                                spacing: 4,
                                runSpacing: 4,
                                children: widget.selectedValues.map((value) {
                                  final item = widget.items.firstWhere(
                                    (item) => item.value == value,
                                  );
                                  return Chip(
                                    label: Text(item.label),
                                    onDeleted: widget.enabled
                                        ? () {
                                            final newValues = List<T>.from(
                                              widget.selectedValues,
                                            );
                                            newValues.remove(value);
                                            widget.onChanged?.call(newValues);
                                          }
                                        : null,
                                    deleteIconColor:
                                        theme.colorScheme.onSurfaceVariant,
                                  );
                                }).toList(),
                              ),
                      ),
                      Icon(
                        _isExpanded ? Icons.expand_less : Icons.expand_more,
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                    ],
                  ),
                ),
              ),
              if (_isExpanded)
                Container(
                  decoration: BoxDecoration(
                    border: Border(
                      top: BorderSide(color: theme.colorScheme.outline),
                    ),
                  ),
                  child: Column(
                    children: widget.items.map((item) {
                      final isSelected = widget.selectedValues.contains(
                        item.value,
                      );
                      final canSelect =
                          widget.maxSelections == null ||
                          widget.selectedValues.length <
                              widget.maxSelections! ||
                          isSelected;

                      return CheckboxListTile(
                        title: Text(item.label),
                        value: isSelected,
                        onChanged: widget.enabled && canSelect
                            ? (bool? checked) {
                                final newValues = List<T>.from(
                                  widget.selectedValues,
                                );
                                if (checked == true) {
                                  newValues.add(item.value);
                                } else {
                                  newValues.remove(item.value);
                                }
                                widget.onChanged?.call(newValues);
                              }
                            : null,
                        secondary: item.icon,
                      );
                    }).toList(),
                  ),
                ),
            ],
          ),
        ),
      ],
    );
  }
}
