import 'base_model.dart';
import 'enums.dart';

/// User model for authentication and role management
class UserModel extends BaseModel with HasValidation {
  final String userId;
  final String password;
  final UserRole role;
  final String? name;
  final String? email;
  final String? division;
  final String? subdivision;

  UserModel({
    required this.userId,
    required this.password,
    required this.role,
    this.name,
    this.email,
    this.division,
    this.subdivision,
    super.createdAt,
    super.updatedAt,
  });

  /// Create UserModel from JSON
  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      userId: json['UserID'] as String,
      password: json['Password'] as String,
      role: UserRole.fromString(json['Role'] as String),
      name: json['Name'] as String?,
      email: json['Email'] as String?,
      division: json['Division'] as String?,
      subdivision: json['Subdivision'] as String?,
      createdAt: BaseModel.parseDateTime(json['CreatedAt'] as String?),
      updatedAt: BaseModel.parseDateTime(json['UpdatedAt'] as String?),
    );
  }

  /// Convert UserModel to JSO<PERSON>
  @override
  Map<String, dynamic> toJson() {
    return {
      'UserID': userId,
      'Password': password,
      'Role': role.code,
      'Name': name,
      'Email': email,
      'Division': division,
      'Subdivision': subdivision,
      'CreatedAt': BaseModel.dateTimeToString(createdAt),
      'UpdatedAt': BaseModel.dateTimeToString(updatedAt),
    };
  }

  /// Create a copy with updated fields
  UserModel copyWith({
    String? userId,
    String? password,
    UserRole? role,
    String? name,
    String? email,
    String? division,
    String? subdivision,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return UserModel(
      userId: userId ?? this.userId,
      password: password ?? this.password,
      role: role ?? this.role,
      name: name ?? this.name,
      email: email ?? this.email,
      division: division ?? this.division,
      subdivision: subdivision ?? this.subdivision,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  UserModel copyWithUpdatedTimestamp() {
    return copyWith(updatedAt: DateTime.now());
  }

  /// Get display name (name or userId)
  String get displayName => name?.isNotEmpty == true ? name! : userId;

  /// Get full display text
  String get fullDisplayText {
    final parts = <String>[displayName];
    if (role.displayName.isNotEmpty) parts.add('(${role.displayName})');
    if (division?.isNotEmpty == true) parts.add(division!);
    return parts.join(' ');
  }

  /// Check if user can access admin features
  bool get canAccessAdmin => role.isAdmin;

  /// Check if user can forward claims
  bool get canForwardClaims => role.canForward;

  /// Check if user can approve claims
  bool get canApproveClaims => role.canApprove;

  /// Get next role for claim forwarding based on amount
  UserRole? getNextForwardRole(double amount) => role.getNextRole(amount);

  /// Validate user model
  @override
  ValidationResult validate() {
    final errors = <String>[];

    // Validate userId
    if (userId.trim().isEmpty) {
      errors.add('User ID is required');
    } else if (userId.length < 3) {
      errors.add('User ID must be at least 3 characters');
    }

    // Validate password
    if (password.trim().isEmpty) {
      errors.add('Password is required');
    } else if (password.length < 6) {
      errors.add('Password must be at least 6 characters');
    }

    // Validate email if provided
    if (email != null && email!.isNotEmpty) {
      final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
      if (!emailRegex.hasMatch(email!)) {
        errors.add('Invalid email format');
      }
    }

    // Validate name if provided
    if (name != null && name!.isNotEmpty && name!.length < 2) {
      errors.add('Name must be at least 2 characters');
    }

    return errors.isEmpty
        ? ValidationResult.valid()
        : ValidationResult.invalid(errors);
  }

  /// Create a user without password (for display purposes)
  UserModel withoutPassword() {
    return copyWith(password: '***');
  }

  /// Check if password matches
  bool checkPassword(String inputPassword) {
    return password == inputPassword;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserModel &&
           other.userId == userId &&
           other.password == password &&
           other.role == role &&
           other.name == name &&
           other.email == email &&
           other.division == division &&
           other.subdivision == subdivision &&
           super == other;
  }

  @override
  int get hashCode => Object.hash(
    super.hashCode,
    userId,
    password,
    role,
    name,
    email,
    division,
    subdivision,
  );

  @override
  String toString() {
    return 'UserModel{userId: $userId, role: ${role.code}, name: $name, division: $division}';
  }
}
