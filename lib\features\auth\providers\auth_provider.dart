import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../common/models/user_model.dart';
import '../../../common/models/enums.dart';
import '../repositories/auth_repository.dart';

part 'auth_provider.g.dart';

/// Authentication state
class AuthState {
  final UserModel? user;
  final bool isLoading;
  final String? error;
  final bool isAuthenticated;

  const AuthState({
    this.user,
    this.isLoading = false,
    this.error,
    this.isAuthenticated = false,
  });

  AuthState copyWith({
    UserModel? user,
    bool? isLoading,
    String? error,
    bool? isAuthenticated,
  }) {
    return AuthState(
      user: user ?? this.user,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      isAuthenticated: isAuthenticated ?? this.isAuthenticated,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AuthState &&
           other.user == user &&
           other.isLoading == isLoading &&
           other.error == error &&
           other.isAuthenticated == isAuthenticated;
  }

  @override
  int get hashCode => Object.hash(user, isLoading, error, isAuthenticated);

  @override
  String toString() {
    return 'AuthState{user: ${user?.userId}, isLoading: $isLoading, error: $error, isAuthenticated: $isAuthenticated}';
  }
}

/// Authentication provider
@riverpod
class Auth extends _$Auth {
  @override
  AuthState build() {
    // Initialize with unauthenticated state
    return const AuthState();
  }

  /// Login with user credentials
  Future<void> login(String userId, String password) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final authRepository = ref.read(authRepositoryProvider);
      final user = await authRepository.login(userId, password);

      if (user != null) {
        state = state.copyWith(
          user: user,
          isLoading: false,
          isAuthenticated: true,
        );
      } else {
        state = state.copyWith(
          isLoading: false,
          error: 'Invalid credentials',
        );
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// Logout current user
  Future<void> logout() async {
    state = state.copyWith(isLoading: true);

    try {
      final authRepository = ref.read(authRepositoryProvider);
      await authRepository.logout();

      state = const AuthState();
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// Check if user has specific role
  bool hasRole(UserRole role) {
    return state.user?.role == role;
  }

  /// Check if user can access admin features
  bool get canAccessAdmin {
    return state.user?.canAccessAdmin ?? false;
  }

  /// Check if user can forward claims
  bool get canForwardClaims {
    return state.user?.canForwardClaims ?? false;
  }

  /// Check if user can approve claims
  bool get canApproveClaims {
    return state.user?.canApproveClaims ?? false;
  }

  /// Get current user's role
  UserRole? get currentUserRole {
    return state.user?.role;
  }

  /// Get current user's display name
  String get currentUserDisplayName {
    return state.user?.displayName ?? 'Unknown User';
  }

  /// Get current user's division
  String? get currentUserDivision {
    return state.user?.division;
  }

  /// Clear error state
  void clearError() {
    state = state.copyWith(error: null);
  }

  /// Refresh current user data
  Future<void> refreshUser() async {
    if (!state.isAuthenticated || state.user == null) return;

    state = state.copyWith(isLoading: true);

    try {
      final authRepository = ref.read(authRepositoryProvider);
      final updatedUser = await authRepository.getUserById(state.user!.userId);

      if (updatedUser != null) {
        state = state.copyWith(
          user: updatedUser,
          isLoading: false,
        );
      } else {
        // User not found, logout
        await logout();
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }
}

/// Provider for checking authentication status
@riverpod
bool isAuthenticated(IsAuthenticatedRef ref) {
  final authState = ref.watch(authProvider);
  return authState.isAuthenticated;
}

/// Provider for current user
@riverpod
UserModel? currentUser(CurrentUserRef ref) {
  final authState = ref.watch(authProvider);
  return authState.user;
}

/// Provider for current user role
@riverpod
UserRole? currentUserRole(CurrentUserRoleRef ref) {
  final authState = ref.watch(authProvider);
  return authState.user?.role;
}

/// Provider for checking if current user is admin
@riverpod
bool isAdmin(IsAdminRef ref) {
  final authState = ref.watch(authProvider);
  return authState.user?.canAccessAdmin ?? false;
}

/// Provider for checking if current user can forward claims
@riverpod
bool canForwardClaims(CanForwardClaimsRef ref) {
  final authState = ref.watch(authProvider);
  return authState.user?.canForwardClaims ?? false;
}

/// Provider for checking if current user can approve claims
@riverpod
bool canApproveClaims(CanApproveClaimsRef ref) {
  final authState = ref.watch(authProvider);
  return authState.user?.canApproveClaims ?? false;
}

/// Provider for getting user's display name
@riverpod
String userDisplayName(UserDisplayNameRef ref) {
  final authState = ref.watch(authProvider);
  return authState.user?.displayName ?? 'Unknown User';
}

/// Provider for getting user's division
@riverpod
String? userDivision(UserDivisionRef ref) {
  final authState = ref.watch(authProvider);
  return authState.user?.division;
}
