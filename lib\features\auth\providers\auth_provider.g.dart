// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'auth_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$isAuthenticatedHash() => r'8f8c4f8c4f8c4f8c4f8c4f8c4f8c4f8c4f8c4f8c';

/// Provider for checking authentication status
///
/// Copied from [isAuthenticated].
@ProviderFor(isAuthenticated)
final isAuthenticatedProvider = AutoDisposeProvider<bool>.internal(
  isAuthenticated,
  name: r'isAuthenticatedProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$isAuthenticatedHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef IsAuthenticatedRef = AutoDisposeProviderRef<bool>;
String _$currentUserHash() => r'9f9c5f9c5f9c5f9c5f9c5f9c5f9c5f9c5f9c5f9c';

/// Provider for current user
///
/// Copied from [currentUser].
@ProviderFor(currentUser)
final currentUserProvider = AutoDisposeProvider<UserModel?>.internal(
  currentUser,
  name: r'currentUserProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$currentUserHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef CurrentUserRef = AutoDisposeProviderRef<UserModel?>;
String _$currentUserRoleHash() => r'af9c6f9c6f9c6f9c6f9c6f9c6f9c6f9c6f9c6f9c';

/// Provider for current user role
///
/// Copied from [currentUserRole].
@ProviderFor(currentUserRole)
final currentUserRoleProvider = AutoDisposeProvider<UserRole?>.internal(
  currentUserRole,
  name: r'currentUserRoleProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$currentUserRoleHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef CurrentUserRoleRef = AutoDisposeProviderRef<UserRole?>;
String _$isAdminHash() => r'bf9c7f9c7f9c7f9c7f9c7f9c7f9c7f9c7f9c7f9c';

/// Provider for checking if current user is admin
///
/// Copied from [isAdmin].
@ProviderFor(isAdmin)
final isAdminProvider = AutoDisposeProvider<bool>.internal(
  isAdmin,
  name: r'isAdminProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$isAdminHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef IsAdminRef = AutoDisposeProviderRef<bool>;
String _$canForwardClaimsHash() => r'cf9c8f9c8f9c8f9c8f9c8f9c8f9c8f9c8f9c8f9c';

/// Provider for checking if current user can forward claims
///
/// Copied from [canForwardClaims].
@ProviderFor(canForwardClaims)
final canForwardClaimsProvider = AutoDisposeProvider<bool>.internal(
  canForwardClaims,
  name: r'canForwardClaimsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$canForwardClaimsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef CanForwardClaimsRef = AutoDisposeProviderRef<bool>;
String _$canApproveClaimsHash() => r'df9c9f9c9f9c9f9c9f9c9f9c9f9c9f9c9f9c9f9c';

/// Provider for checking if current user can approve claims
///
/// Copied from [canApproveClaims].
@ProviderFor(canApproveClaims)
final canApproveClaimsProvider = AutoDisposeProvider<bool>.internal(
  canApproveClaims,
  name: r'canApproveClaimsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$canApproveClaimsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef CanApproveClaimsRef = AutoDisposeProviderRef<bool>;
String _$userDisplayNameHash() => r'ef9caf9caf9caf9caf9caf9caf9caf9caf9caf9c';

/// Provider for getting user's display name
///
/// Copied from [userDisplayName].
@ProviderFor(userDisplayName)
final userDisplayNameProvider = AutoDisposeProvider<String>.internal(
  userDisplayName,
  name: r'userDisplayNameProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$userDisplayNameHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef UserDisplayNameRef = AutoDisposeProviderRef<String>;
String _$userDivisionHash() => r'ff9cbf9cbf9cbf9cbf9cbf9cbf9cbf9cbf9cbf9c';

/// Provider for getting user's division
///
/// Copied from [userDivision].
@ProviderFor(userDivision)
final userDivisionProvider = AutoDisposeProvider<String?>.internal(
  userDivision,
  name: r'userDivisionProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$userDivisionHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef UserDivisionRef = AutoDisposeProviderRef<String?>;
String _$authHash() => r'1f9ccf9ccf9ccf9ccf9ccf9ccf9ccf9ccf9ccf9c';

/// Authentication provider
///
/// Copied from [Auth].
@ProviderFor(Auth)
final authProvider = AutoDisposeNotifierProvider<Auth, AuthState>.internal(
  Auth.new,
  name: r'authProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$authHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$Auth = AutoDisposeNotifier<AuthState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
