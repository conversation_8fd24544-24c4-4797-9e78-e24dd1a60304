import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../core/theme/app_theme.dart';
import '../../auth/providers/auth_provider.dart';
import '../widgets/dashboard_card.dart';
import '../widgets/stats_card.dart';
import '../widgets/recent_activity_card.dart';
import '../widgets/quick_actions_card.dart';

/// Professional ERP Dashboard screen
class DashboardScreen extends ConsumerWidget {
  const DashboardScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authState = ref.watch(authProvider);
    final user = authState.user;

    return Padding(
      padding: const EdgeInsets.all(24),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Welcome section
            _buildWelcomeSection(user),
            const SizedBox(height: 24),

            // Stats cards
            _buildStatsSection(),
            const SizedBox(height: 24),

            // Main content grid
            _buildMainContent(user),
          ],
        ),
      ),
    );
  }

  Widget _buildWelcomeSection(user) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppTheme.primaryColor,
            AppTheme.primaryColor.withValues(alpha: 0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Welcome back, ${user?.name ?? 'User'}!',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Here\'s what\'s happening with your medical claims today.',
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 0.9),
                    fontSize: 16,
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  'Role: ${user?.role.displayName ?? 'Unknown'}',
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 0.8),
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(Icons.dashboard, color: Colors.white, size: 48),
          ),
        ],
      ),
    );
  }

  Widget _buildStatsSection() {
    return StatsGrid(
      cards: [
        StatsCard(
          title: 'Total Claims',
          value: '1,234',
          subtitle: 'This month',
          icon: Icons.medical_services,
          color: Colors.blue,
          trend: '+12%',
          isPositiveTrend: true,
        ),
        StatsCard(
          title: 'Pending Claims',
          value: '89',
          subtitle: 'Awaiting approval',
          icon: Icons.pending_actions,
          color: Colors.orange,
          trend: '-5%',
          isPositiveTrend: false,
        ),
        StatsCard(
          title: 'Approved Claims',
          value: '1,045',
          subtitle: 'This month',
          icon: Icons.check_circle,
          color: Colors.green,
          trend: '+18%',
          isPositiveTrend: true,
        ),
        StatsCard(
          title: 'Total Amount',
          value: '₹12.5L',
          subtitle: 'This month',
          icon: Icons.account_balance_wallet,
          color: Colors.purple,
          trend: '+25%',
          isPositiveTrend: true,
        ),
      ],
    );
  }

  Widget _buildMainContent(user) {
    return Column(
      children: [
        QuickActionsCard(userRole: user?.role),
        const SizedBox(height: 24),
        const RecentActivityCard(),
        const SizedBox(height: 24),
        _buildClaimsSummaryCard(),
        const SizedBox(height: 24),
        _buildUpcomingTasksCard(),
      ],
    );
  }

  Widget _buildClaimsSummaryCard() {
    return DashboardCard(
      title: 'Claims Summary',
      child: Column(
        children: [
          _buildSummaryItem('Pending Review', '23', Colors.orange),
          const SizedBox(height: 12),
          _buildSummaryItem('Approved Today', '15', Colors.green),
          const SizedBox(height: 12),
          _buildSummaryItem('Rejected', '3', Colors.red),
          const SizedBox(height: 12),
          _buildSummaryItem('In Process', '45', Colors.blue),
        ],
      ),
    );
  }

  Widget _buildUpcomingTasksCard() {
    return DashboardCard(
      title: 'Upcoming Tasks',
      child: Column(
        children: [
          _buildTaskItem('Review pending claims', 'Due in 2 hours'),
          const SizedBox(height: 12),
          _buildTaskItem('Monthly report generation', 'Due tomorrow'),
          const SizedBox(height: 12),
          _buildTaskItem('User access review', 'Due in 3 days'),
        ],
      ),
    );
  }

  Widget _buildSummaryItem(String title, String count, Color color) {
    return Row(
      children: [
        Container(
          width: 8,
          height: 8,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(4),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            title,
            style: TextStyle(color: AppTheme.textSecondary, fontSize: 14),
          ),
        ),
        Text(
          count,
          style: const TextStyle(
            color: AppTheme.textPrimary,
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  Widget _buildTaskItem(String title, String dueDate) {
    return Row(
      children: [
        Icon(Icons.circle_outlined, size: 16, color: AppTheme.textSecondary),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: const TextStyle(
                  color: AppTheme.textPrimary,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                dueDate,
                style: TextStyle(color: AppTheme.textSecondary, fontSize: 12),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
