import 'package:sqflite_common_ffi/sqflite_ffi.dart';

import 'db_service.dart';
import 'db_schema.dart';

/// Database helper class with common query operations
/// Provides high-level methods for database operations
class DatabaseHelper {
  static final DatabaseService _dbService = DatabaseService.instance;

  // ==================== USER OPERATIONS ====================

  /// Authenticate user login
  static Future<Map<String, dynamic>?> authenticateUser(
    String userId,
    String password,
  ) async {
    final result = await _dbService.query(
      DatabaseSchema.usersTable,
      where: 'LOWER(UserID) = LOWER(?) AND Password = ?',
      whereArgs: [userId, password],
    );
    return result.isNotEmpty ? result.first : null;
  }

  /// Get user by ID
  static Future<Map<String, dynamic>?> getUserById(String userId) async {
    final result = await _dbService.query(
      DatabaseSchema.usersTable,
      where: 'UserID = ?',
      whereArgs: [userId],
    );
    return result.isNotEmpty ? result.first : null;
  }

  /// Get all users
  static Future<List<Map<String, dynamic>>> getAllUsers() async {
    return await _dbService.query(
      DatabaseSchema.usersTable,
      orderBy: 'Name ASC',
    );
  }

  /// Insert or update user
  static Future<int> saveUser(Map<String, dynamic> userData) async {
    userData['UpdatedAt'] = DateTime.now().toIso8601String();
    return await _dbService.insert(DatabaseSchema.usersTable, userData);
  }

  /// Delete user
  static Future<int> deleteUser(String userId) async {
    return await _dbService.delete(DatabaseSchema.usersTable, 'UserID = ?', [
      userId,
    ]);
  }

  // ==================== CLAIM MASTER OPERATIONS ====================

  /// Get claim from ClaimMaster by ClaimNo
  static Future<Map<String, dynamic>?> getClaimMaster(String claimNo) async {
    final result = await _dbService.query(
      DatabaseSchema.claimMasterTable,
      where: 'ClaimNo = ?',
      whereArgs: [claimNo],
    );
    return result.isNotEmpty ? result.first : null;
  }

  /// Insert or update claim in ClaimMaster
  static Future<int> saveClaimMaster(Map<String, dynamic> claimData) async {
    claimData['UpdatedAt'] = DateTime.now().toIso8601String();
    return await _dbService.insert(DatabaseSchema.claimMasterTable, claimData);
  }

  /// Bulk insert claims to ClaimMaster
  static Future<void> bulkInsertClaimMaster(
    List<Map<String, dynamic>> claims,
  ) async {
    final db = await _dbService.database;
    final batch = db.batch();

    for (var claim in claims) {
      claim['UpdatedAt'] = DateTime.now().toIso8601String();
      batch.insert(
        DatabaseSchema.claimMasterTable,
        claim,
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    }

    await batch.commit();
  }

  /// Get all claims from ClaimMaster with pagination
  static Future<List<Map<String, dynamic>>> getClaimMasterList({
    int? limit,
    int? offset,
    String? searchTerm,
  }) async {
    String? where;
    List<dynamic>? whereArgs;

    if (searchTerm != null && searchTerm.isNotEmpty) {
      where = 'ClaimNo LIKE ? OR Name LIKE ? OR EmployeeNumber LIKE ?';
      whereArgs = ['%$searchTerm%', '%$searchTerm%', '%$searchTerm%'];
    }

    return await _dbService.query(
      DatabaseSchema.claimMasterTable,
      where: where,
      whereArgs: whereArgs,
      orderBy: 'CreatedAt DESC',
      limit: limit,
      offset: offset,
    );
  }

  // ==================== MEDICAL CLAIM REGISTER OPERATIONS ====================

  /// Get claim from MedicalClaimRegister
  static Future<Map<String, dynamic>?> getMedicalClaim(String claimNo) async {
    final result = await _dbService.query(
      DatabaseSchema.medicalClaimRegisterTable,
      where: 'ClaimNo = ?',
      whereArgs: [claimNo],
    );
    return result.isNotEmpty ? result.first : null;
  }

  /// Save claim to MedicalClaimRegister
  static Future<int> saveMedicalClaim(Map<String, dynamic> claimData) async {
    claimData['UpdatedAt'] = DateTime.now().toIso8601String();
    return await _dbService.insert(
      DatabaseSchema.medicalClaimRegisterTable,
      claimData,
    );
  }

  /// Get claims by PHStatus
  static Future<List<Map<String, dynamic>>> getClaimsByPHStatus(
    String phStatus,
  ) async {
    return await _dbService.query(
      DatabaseSchema.medicalClaimRegisterTable,
      where: 'PHStatus = ?',
      whereArgs: [phStatus],
      orderBy: 'ForwardedDate DESC, CreatedAt DESC',
    );
  }

  /// Get claims for dashboard counts
  static Future<Map<String, int>> getDashboardCounts() async {
    final counts = <String, int>{};

    final phStatuses = [
      'SDN',
      'DN',
      'CR',
      'CO',
      'Doctor',
      'Returned',
      'In Payment',
      'Paid',
    ];

    for (String status in phStatuses) {
      final result = await _dbService.rawQuery(
        'SELECT COUNT(*) as count FROM ${DatabaseSchema.medicalClaimRegisterTable} WHERE PHStatus = ?',
        [status],
      );
      counts[status] = result.first['count'] as int;
    }

    return counts;
  }

  /// Search claims with filters
  static Future<List<Map<String, dynamic>>> searchClaims({
    String? claimNo,
    String? employeeNo,
    String? phStatus,
    String? status,
    int? limit,
    int? offset,
  }) async {
    List<String> conditions = [];
    List<dynamic> args = [];

    if (claimNo != null && claimNo.isNotEmpty) {
      conditions.add('ClaimNo LIKE ?');
      args.add('%$claimNo%');
    }

    if (employeeNo != null && employeeNo.isNotEmpty) {
      conditions.add('EmployeeNumber LIKE ?');
      args.add('%$employeeNo%');
    }

    if (phStatus != null && phStatus.isNotEmpty) {
      conditions.add('PHStatus = ?');
      args.add(phStatus);
    }

    if (status != null && status.isNotEmpty) {
      conditions.add('Status = ?');
      args.add(status);
    }

    String? where = conditions.isNotEmpty ? conditions.join(' AND ') : null;

    return await _dbService.query(
      DatabaseSchema.medicalClaimRegisterTable,
      where: where,
      whereArgs: args.isNotEmpty ? args : null,
      orderBy: 'UpdatedAt DESC',
      limit: limit,
      offset: offset,
    );
  }

  // ==================== DOCTOR OPERATIONS ====================

  /// Get all active doctors
  static Future<List<Map<String, dynamic>>> getActiveDoctors() async {
    return await _dbService.query(
      DatabaseSchema.doctorsRegisterTable,
      where: 'IsActive = ?',
      whereArgs: [1],
      orderBy: 'Name ASC',
    );
  }

  /// Search doctors by name
  static Future<List<Map<String, dynamic>>> searchDoctors(
    String searchTerm, {
    int limit = 5,
  }) async {
    return await _dbService.query(
      DatabaseSchema.doctorsRegisterTable,
      where: 'Name LIKE ? AND IsActive = ?',
      whereArgs: ['%$searchTerm%', 1],
      orderBy: 'Name ASC',
      limit: limit,
    );
  }

  /// Save doctor
  static Future<int> saveDoctor(Map<String, dynamic> doctorData) async {
    doctorData['UpdatedAt'] = DateTime.now().toIso8601String();
    return await _dbService.insert(
      DatabaseSchema.doctorsRegisterTable,
      doctorData,
    );
  }

  // ==================== CONSULTATION OPERATIONS ====================

  /// Get consultations for a claim
  static Future<List<Map<String, dynamic>>> getConsultations(
    String claimNo,
  ) async {
    return await _dbService.query(
      DatabaseSchema.consultationRegisterTable,
      where: 'ClaimNo = ?',
      whereArgs: [claimNo],
      orderBy: 'ConsultationDate DESC',
    );
  }

  /// Save consultation
  static Future<int> saveConsultation(
    Map<String, dynamic> consultationData,
  ) async {
    consultationData['UpdatedAt'] = DateTime.now().toIso8601String();
    return await _dbService.insert(
      DatabaseSchema.consultationRegisterTable,
      consultationData,
    );
  }

  // ==================== CLAIM STATUS HISTORY OPERATIONS ====================

  /// Add claim status history entry
  static Future<int> addClaimStatusHistory({
    required String claimNo,
    required String action,
    String? fromUser,
    String? toUser,
    String? remarks,
  }) async {
    final historyData = {
      'ClaimNo': claimNo,
      'Action': action,
      'FromUser': fromUser,
      'ToUser': toUser,
      'ActionDate': DateTime.now().toIso8601String(),
      'Remarks': remarks,
    };

    return await _dbService.insert(
      DatabaseSchema.claimStatusHistoryTable,
      historyData,
    );
  }

  /// Get claim status history
  static Future<List<Map<String, dynamic>>> getClaimStatusHistory(
    String claimNo,
  ) async {
    return await _dbService.query(
      DatabaseSchema.claimStatusHistoryTable,
      where: 'ClaimNo = ?',
      whereArgs: [claimNo],
      orderBy: 'ActionDate DESC',
    );
  }
}
