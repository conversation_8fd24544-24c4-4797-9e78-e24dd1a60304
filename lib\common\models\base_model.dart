/// Base model class for all data models
/// Provides common functionality like JSON serialization and timestamp handling
abstract class BaseModel {
  final DateTime? createdAt;
  final DateTime? updatedAt;

  BaseModel({
    this.createdAt,
    this.updatedAt,
  });

  /// Convert model to JSON map
  Map<String, dynamic> toJson();

  /// Create model from JSON map
  static BaseModel fromJson(Map<String, dynamic> json) {
    throw UnimplementedError('fromJson must be implemented by subclasses');
  }

  /// Get formatted created date
  String get formattedCreatedAt {
    if (createdAt == null) return '';
    return _formatDateTime(createdAt!);
  }

  /// Get formatted updated date
  String get formattedUpdatedAt {
    if (updatedAt == null) return '';
    return _formatDateTime(updatedAt!);
  }

  /// Format DateTime to readable string
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day.toString().padLeft(2, '0')}/'
           '${dateTime.month.toString().padLeft(2, '0')}/'
           '${dateTime.year} '
           '${dateTime.hour.toString().padLeft(2, '0')}:'
           '${dateTime.minute.toString().padLeft(2, '0')}';
  }

  /// Parse DateTime from ISO string
  static DateTime? parseDateTime(String? dateTimeString) {
    if (dateTimeString == null || dateTimeString.isEmpty) return null;
    try {
      return DateTime.parse(dateTimeString);
    } catch (e) {
      return null;
    }
  }

  /// Convert DateTime to ISO string for database storage
  static String? dateTimeToString(DateTime? dateTime) {
    return dateTime?.toIso8601String();
  }

  /// Copy with updated timestamp
  BaseModel copyWithUpdatedTimestamp();

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is BaseModel &&
           other.runtimeType == runtimeType &&
           other.createdAt == createdAt &&
           other.updatedAt == updatedAt;
  }

  @override
  int get hashCode => Object.hash(runtimeType, createdAt, updatedAt);

  @override
  String toString() {
    return '$runtimeType{createdAt: $createdAt, updatedAt: $updatedAt}';
  }
}

/// Mixin for models that have ID fields
mixin HasId<T> {
  T get id;
}

/// Mixin for models that can be activated/deactivated
mixin HasActiveStatus {
  bool get isActive;
  
  BaseModel copyWithActiveStatus(bool isActive);
}

/// Mixin for models that have user tracking
mixin HasUserTracking {
  String? get createdBy;
  String? get updatedBy;
  
  BaseModel copyWithUserTracking({String? createdBy, String? updatedBy});
}

/// Mixin for models that support soft delete
mixin HasSoftDelete {
  bool get isDeleted;
  DateTime? get deletedAt;
  String? get deletedBy;
  
  BaseModel copyWithSoftDelete({
    bool? isDeleted,
    DateTime? deletedAt,
    String? deletedBy,
  });
}

/// Validation result class
class ValidationResult {
  final bool isValid;
  final List<String> errors;

  ValidationResult({
    required this.isValid,
    this.errors = const [],
  });

  ValidationResult.valid() : this(isValid: true);
  
  ValidationResult.invalid(List<String> errors) : this(
    isValid: false,
    errors: errors,
  );

  /// Get first error message
  String? get firstError => errors.isNotEmpty ? errors.first : null;

  /// Get all errors as a single string
  String get allErrors => errors.join(', ');
}

/// Mixin for models that support validation
mixin HasValidation {
  /// Validate the model
  ValidationResult validate();

  /// Check if model is valid
  bool get isValid => validate().isValid;

  /// Get validation errors
  List<String> get validationErrors => validate().errors;
}
