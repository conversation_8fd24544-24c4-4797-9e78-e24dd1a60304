import 'package:flutter/material.dart';

import '../../../core/theme/app_theme.dart';
import '../../../common/models/enums.dart';
import 'dashboard_card.dart';

/// Recent activity card for dashboard
class RecentActivityCard extends StatelessWidget {
  const RecentActivityCard({super.key});

  @override
  Widget build(BuildContext context) {
    final activities = _getMockActivities();

    return DashboardCard(
      title: 'Recent Activity',
      actions: [
        TextButton(
          onPressed: () {
            // TODO: Navigate to full activity log
          },
          child: const Text('View All'),
        ),
      ],
      child: activities.isEmpty
          ? _buildEmptyState()
          : ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: activities.length,
              separatorBuilder: (context, index) => Divider(
                color: AppTheme.borderColor,
                height: 1,
              ),
              itemBuilder: (context, index) => _buildActivityItem(activities[index]),
            ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.history,
            size: 48,
            color: AppTheme.textSecondary,
          ),
          const SizedBox(height: 16),
          Text(
            'No recent activity',
            style: TextStyle(
              color: AppTheme.textSecondary,
              fontSize: 16,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActivityItem(ActivityItem activity) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 12),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: activity.color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              activity.icon,
              color: activity.color,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  activity.title,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: AppTheme.textPrimary,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  activity.description,
                  style: TextStyle(
                    fontSize: 12,
                    color: AppTheme.textSecondary,
                  ),
                ),
              ],
            ),
          ),
          Text(
            activity.time,
            style: TextStyle(
              fontSize: 11,
              color: AppTheme.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  List<ActivityItem> _getMockActivities() {
    return [
      ActivityItem(
        title: 'New claim submitted',
        description: 'CLM001 - John Doe submitted a new medical claim',
        time: '2 min ago',
        icon: Icons.add_circle,
        color: Colors.green,
      ),
      ActivityItem(
        title: 'Claim approved',
        description: 'CLM002 - Medical claim approved by Dr. Smith',
        time: '15 min ago',
        icon: Icons.check_circle,
        color: Colors.blue,
      ),
      ActivityItem(
        title: 'Payment processed',
        description: 'CLM003 - Payment of ₹5,000 processed successfully',
        time: '1 hour ago',
        icon: Icons.payment,
        color: Colors.purple,
      ),
      ActivityItem(
        title: 'Claim returned',
        description: 'CLM004 - Claim returned for additional documentation',
        time: '2 hours ago',
        icon: Icons.undo,
        color: Colors.orange,
      ),
      ActivityItem(
        title: 'User registered',
        description: 'New user Dr. Johnson registered in the system',
        time: '3 hours ago',
        icon: Icons.person_add,
        color: Colors.teal,
      ),
    ];
  }
}

/// Activity item model
class ActivityItem {
  final String title;
  final String description;
  final String time;
  final IconData icon;
  final Color color;

  const ActivityItem({
    required this.title,
    required this.description,
    required this.time,
    required this.icon,
    required this.color,
  });
}
