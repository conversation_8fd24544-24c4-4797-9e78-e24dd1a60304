import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../common/models/enums.dart';
import '../../features/auth/providers/auth_provider.dart';
import '../theme/app_theme.dart';
import 'sidebar_menu.dart';
import 'top_navigation.dart';
import 'breadcrumb_navigation.dart';

/// Main app shell with navigation for ERP system
class AppShell extends ConsumerStatefulWidget {
  final Widget child;
  final String currentPath;

  const AppShell({
    super.key,
    required this.child,
    required this.currentPath,
  });

  @override
  ConsumerState<AppShell> createState() => _AppShellState();
}

class _AppShellState extends ConsumerState<AppShell> {
  bool _isSidebarCollapsed = false;
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(authProvider);
    final user = authState.user;
    
    if (user == null) {
      return widget.child; // Return login screen without shell
    }

    final isDesktop = MediaQuery.of(context).size.width > 768;

    return Scaffold(
      key: _scaffoldKey,
      backgroundColor: AppTheme.backgroundColor,
      drawer: isDesktop ? null : SidebarMenu(
        isCollapsed: false,
        onToggle: () {},
        currentPath: widget.currentPath,
      ),
      body: Row(
        children: [
          // Sidebar for desktop
          if (isDesktop)
            SidebarMenu(
              isCollapsed: _isSidebarCollapsed,
              onToggle: () {
                setState(() {
                  _isSidebarCollapsed = !_isSidebarCollapsed;
                });
              },
              currentPath: widget.currentPath,
            ),
          
          // Main content area
          Expanded(
            child: Column(
              children: [
                // Top navigation bar
                TopNavigation(
                  onMenuToggle: () {
                    if (isDesktop) {
                      setState(() {
                        _isSidebarCollapsed = !_isSidebarCollapsed;
                      });
                    } else {
                      _scaffoldKey.currentState?.openDrawer();
                    }
                  },
                  currentPath: widget.currentPath,
                ),
                
                // Breadcrumb navigation
                BreadcrumbNavigation(currentPath: widget.currentPath),
                
                // Main content
                Expanded(
                  child: Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(24),
                    child: widget.child,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

/// Navigation item model
class NavigationItem {
  final String title;
  final IconData icon;
  final String path;
  final List<UserRole>? allowedRoles;
  final List<NavigationItem>? children;
  final bool isExpanded;

  const NavigationItem({
    required this.title,
    required this.icon,
    required this.path,
    this.allowedRoles,
    this.children,
    this.isExpanded = false,
  });

  bool canAccess(UserRole? userRole) {
    if (allowedRoles == null) return true;
    if (userRole == null) return false;
    return allowedRoles!.contains(userRole) || userRole == UserRole.admin;
  }
}

/// Navigation items configuration
class NavigationConfig {
  static const List<NavigationItem> items = [
    NavigationItem(
      title: 'Dashboard',
      icon: Icons.dashboard,
      path: '/dashboard',
    ),
    NavigationItem(
      title: 'Claims Management',
      icon: Icons.medical_services,
      path: '/claims',
      children: [
        NavigationItem(
          title: 'View Claims',
          icon: Icons.list,
          path: '/claims',
        ),
        NavigationItem(
          title: 'New Claim',
          icon: Icons.add,
          path: '/claims/new',
        ),
        NavigationItem(
          title: 'Upload Claims',
          icon: Icons.upload,
          path: '/claims/upload',
        ),
      ],
    ),
    NavigationItem(
      title: 'Reports',
      icon: Icons.analytics,
      path: '/reports',
      children: [
        NavigationItem(
          title: 'Claim Reports',
          icon: Icons.assessment,
          path: '/reports/claims',
        ),
        NavigationItem(
          title: 'User Reports',
          icon: Icons.people,
          path: '/reports/users',
        ),
        NavigationItem(
          title: 'Financial Reports',
          icon: Icons.account_balance,
          path: '/reports/financial',
        ),
      ],
    ),
    NavigationItem(
      title: 'Doctor Panel',
      icon: Icons.local_hospital,
      path: '/doctor',
      allowedRoles: [UserRole.doctor, UserRole.admin],
      children: [
        NavigationItem(
          title: 'Pending Claims',
          icon: Icons.pending_actions,
          path: '/doctor/pending',
        ),
        NavigationItem(
          title: 'Vouchers',
          icon: Icons.receipt,
          path: '/doctor/vouchers',
        ),
        NavigationItem(
          title: 'Consultations',
          icon: Icons.medical_information,
          path: '/doctor/consultations',
        ),
      ],
    ),
    NavigationItem(
      title: 'Administration',
      icon: Icons.admin_panel_settings,
      path: '/admin',
      allowedRoles: [UserRole.admin],
      children: [
        NavigationItem(
          title: 'User Management',
          icon: Icons.people_outline,
          path: '/admin/users',
        ),
        NavigationItem(
          title: 'System Settings',
          icon: Icons.settings,
          path: '/admin/settings',
        ),
        NavigationItem(
          title: 'Audit Logs',
          icon: Icons.history,
          path: '/admin/logs',
        ),
      ],
    ),
    NavigationItem(
      title: 'Profile',
      icon: Icons.person,
      path: '/profile',
    ),
  ];

  static List<NavigationItem> getAccessibleItems(UserRole? userRole) {
    return items.where((item) => item.canAccess(userRole)).toList();
  }

  static String getPageTitle(String path) {
    for (final item in items) {
      if (item.path == path) return item.title;
      if (item.children != null) {
        for (final child in item.children!) {
          if (child.path == path) return child.title;
        }
      }
    }
    return 'Medical Bills ERP';
  }

  static List<String> getBreadcrumbs(String path) {
    final breadcrumbs = <String>[];
    
    for (final item in items) {
      if (item.path == path) {
        breadcrumbs.add(item.title);
        break;
      }
      if (item.children != null) {
        for (final child in item.children!) {
          if (child.path == path) {
            breadcrumbs.add(item.title);
            breadcrumbs.add(child.title);
            break;
          }
        }
      }
    }
    
    return breadcrumbs;
  }
}
