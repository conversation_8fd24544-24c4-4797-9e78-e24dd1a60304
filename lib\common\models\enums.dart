/// Enums for Medical Bills ERP System
library;

/// User roles in the system
enum UserRole {
  sdn('<PERSON><PERSON>', 'Sub Divisional Officer'),
  dn('D<PERSON>', 'Divisional Officer'),
  cr('CR', 'Chief Registrar'),
  co('CO', 'Chief Officer'),
  doctor('Doctor', 'Medical Doctor'),
  admin('Admin', 'System Administrator');

  const UserRole(this.code, this.displayName);

  final String code;
  final String displayName;

  static UserRole fromString(String role) {
    return UserRole.values.firstWhere(
      (r) => r.code.toLowerCase() == role.toLowerCase(),
      orElse: () => UserRole.sdn,
    );
  }

  /// Get next role in forwarding hierarchy
  UserRole? getNextRole(double amount) {
    switch (this) {
      case UserRole.sdn:
        return UserRole.dn;
      case UserRole.dn:
        return amount <= 25000 ? UserRole.doctor : UserRole.cr;
      case UserRole.cr:
        return amount <= 50000 ? UserRole.doctor : UserRole.co;
      case UserRole.co:
        return UserRole.doctor;
      case UserRole.doctor:
      case UserRole.admin:
        return null;
    }
  }

  /// Check if role can forward claims
  bool get canForward {
    return this != UserRole.doctor && this != UserRole.admin;
  }

  /// Check if role can approve/reject claims
  bool get canApprove {
    return this == UserRole.doctor;
  }

  /// Check if role has admin privileges
  bool get isAdmin {
    return this == UserRole.admin;
  }
}

/// PHStatus - where the bill currently resides
enum PHStatus {
  sdn('SDN', 'Pending at SDN'),
  dn('DN', 'Pending at DN'),
  cr('CR', 'Pending at CR'),
  co('CO', 'Pending at CO'),
  doctor('Doctor', 'Submitted to Doctor'),
  returned('Returned', 'Returned'),
  inPayment('In Payment', 'In Payment'),
  paid('Paid', 'Paid');

  const PHStatus(this.code, this.displayName);

  final String code;
  final String displayName;

  static PHStatus fromString(String status) {
    return PHStatus.values.firstWhere(
      (s) => s.code.toLowerCase() == status.toLowerCase(),
      orElse: () => PHStatus.sdn,
    );
  }

  /// Get color for status display
  String get colorHex {
    switch (this) {
      case PHStatus.sdn:
        return '#FF9800'; // Orange
      case PHStatus.dn:
        return '#2196F3'; // Blue
      case PHStatus.cr:
        return '#9C27B0'; // Purple
      case PHStatus.co:
        return '#673AB7'; // Deep Purple
      case PHStatus.doctor:
        return '#4CAF50'; // Green
      case PHStatus.returned:
        return '#F44336'; // Red
      case PHStatus.inPayment:
        return '#FF5722'; // Deep Orange
      case PHStatus.paid:
        return '#8BC34A'; // Light Green
    }
  }
}

/// Claim status - final action taken
enum ClaimStatus {
  pending('Pending', 'Pending'),
  approved('Approved', 'Approved'),
  rejected('Rejected', 'Rejected'),
  returned('Returned', 'Returned');

  const ClaimStatus(this.code, this.displayName);

  final String code;
  final String displayName;

  static ClaimStatus fromString(String status) {
    return ClaimStatus.values.firstWhere(
      (s) => s.code.toLowerCase() == status.toLowerCase(),
      orElse: () => ClaimStatus.pending,
    );
  }
}

/// Claim type - OPD or IPD
enum ClaimType {
  opd('OPD', 'Out Patient Department'),
  ipd('IPD', 'In Patient Department');

  const ClaimType(this.code, this.displayName);

  final String code;
  final String displayName;

  static ClaimType fromString(String type) {
    return ClaimType.values.firstWhere(
      (t) => t.code.toLowerCase() == type.toLowerCase(),
      orElse: () => ClaimType.opd,
    );
  }
}

/// Patient relation types
enum PatientRelation {
  self('Self', 'Self'),
  spouse('Spouse', 'Spouse'),
  father('Father', 'Father'),
  mother('Mother', 'Mother'),
  son('Son', 'Son'),
  daughter('Daughter', 'Daughter'),
  other('Other', 'Other');

  const PatientRelation(this.code, this.displayName);

  final String code;
  final String displayName;

  static PatientRelation fromString(String relation) {
    return PatientRelation.values.firstWhere(
      (r) => r.code.toLowerCase() == relation.toLowerCase(),
      orElse: () => PatientRelation.self,
    );
  }
}

/// Treatment types
enum TreatmentType {
  surgery('Surgery', 'Surgery'),
  consultation('Consultation', 'Consultation'),
  diagnostic('Diagnostic', 'Diagnostic Tests'),
  medicine('Medicine', 'Medicine'),
  emergency('Emergency', 'Emergency Treatment'),
  other('Other', 'Other');

  const TreatmentType(this.code, this.displayName);

  final String code;
  final String displayName;

  static TreatmentType fromString(String type) {
    return TreatmentType.values.firstWhere(
      (t) => t.code.toLowerCase() == type.toLowerCase(),
      orElse: () => TreatmentType.consultation,
    );
  }
}

/// Action types for claim status history
enum ClaimAction {
  created('Created', 'Claim Created'),
  forwarded('Forwarded', 'Claim Forwarded'),
  returned('Returned', 'Claim Returned'),
  approved('Approved', 'Claim Approved'),
  rejected('Rejected', 'Claim Rejected'),
  modified('Modified', 'Claim Modified'),
  printed('Printed', 'Forwarding Letter Printed'),
  received('Received', 'Claim Received'),
  voucherGenerated('Voucher Generated', 'Doctor Voucher Generated');

  const ClaimAction(this.code, this.displayName);

  final String code;
  final String displayName;

  static ClaimAction fromString(String action) {
    return ClaimAction.values.firstWhere(
      (a) => a.code.toLowerCase() == action.toLowerCase(),
      orElse: () => ClaimAction.created,
    );
  }
}

/// Hospital stay options
enum HospitalStay {
  yes('Yes', 'Yes'),
  no('No', 'No');

  const HospitalStay(this.code, this.displayName);

  final String code;
  final String displayName;

  static HospitalStay fromString(String stay) {
    return HospitalStay.values.firstWhere(
      (s) => s.code.toLowerCase() == stay.toLowerCase(),
      orElse: () => HospitalStay.no,
    );
  }

  /// Get doctor voucher amount based on hospital stay
  double get voucherAmount {
    switch (this) {
      case HospitalStay.yes:
        return 20.0;
      case HospitalStay.no:
        return 10.0;
    }
  }
}
