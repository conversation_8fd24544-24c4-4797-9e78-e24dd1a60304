import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../../../core/theme/app_theme.dart';
import '../../../common/models/enums.dart';
import 'dashboard_card.dart';

/// Quick actions card for dashboard
class QuickActionsCard extends StatelessWidget {
  final UserRole? userRole;

  const QuickActionsCard({
    super.key,
    this.userRole,
  });

  @override
  Widget build(BuildContext context) {
    final actions = _getActionsForRole(userRole);

    return DashboardCard(
      title: 'Quick Actions',
      child: GridView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: _getCrossAxisCount(context),
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
          childAspectRatio: 1.2,
        ),
        itemCount: actions.length,
        itemBuilder: (context, index) => _buildActionButton(
          context,
          actions[index],
        ),
      ),
    );
  }

  int _getCrossAxisCount(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    if (width > 1200) return 4;
    if (width > 800) return 3;
    return 2;
  }

  List<QuickAction> _getActionsForRole(UserRole? role) {
    final commonActions = [
      QuickAction(
        title: 'View Claims',
        icon: Icons.list,
        color: Colors.blue,
        path: '/claims',
      ),
      QuickAction(
        title: 'New Claim',
        icon: Icons.add,
        color: Colors.green,
        path: '/claims/new',
      ),
    ];

    switch (role) {
      case UserRole.admin:
        return [
          ...commonActions,
          QuickAction(
            title: 'User Management',
            icon: Icons.people,
            color: Colors.purple,
            path: '/admin/users',
          ),
          QuickAction(
            title: 'Reports',
            icon: Icons.analytics,
            color: Colors.orange,
            path: '/reports',
          ),
          QuickAction(
            title: 'System Settings',
            icon: Icons.settings,
            color: Colors.grey,
            path: '/admin/settings',
          ),
          QuickAction(
            title: 'Upload Claims',
            icon: Icons.upload,
            color: Colors.indigo,
            path: '/claims/upload',
          ),
        ];
      case UserRole.doctor:
        return [
          ...commonActions,
          QuickAction(
            title: 'Pending Claims',
            icon: Icons.pending_actions,
            color: Colors.amber,
            path: '/doctor/pending',
          ),
          QuickAction(
            title: 'Consultations',
            icon: Icons.medical_information,
            color: Colors.teal,
            path: '/doctor/consultations',
          ),
          QuickAction(
            title: 'Vouchers',
            icon: Icons.receipt,
            color: Colors.cyan,
            path: '/doctor/vouchers',
          ),
        ];
      default:
        return [
          ...commonActions,
          QuickAction(
            title: 'Upload Claims',
            icon: Icons.upload,
            color: Colors.indigo,
            path: '/claims/upload',
          ),
          QuickAction(
            title: 'Reports',
            icon: Icons.analytics,
            color: Colors.orange,
            path: '/reports',
          ),
        ];
    }
  }

  Widget _buildActionButton(BuildContext context, QuickAction action) {
    return Container(
      decoration: BoxDecoration(
        color: action.color.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: action.color.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => context.go(action.path),
          borderRadius: BorderRadius.circular(8),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  action.icon,
                  color: action.color,
                  size: 32,
                ),
                const SizedBox(height: 8),
                Text(
                  action.title,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: action.color,
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

/// Quick action model
class QuickAction {
  final String title;
  final IconData icon;
  final Color color;
  final String path;

  const QuickAction({
    required this.title,
    required this.icon,
    required this.color,
    required this.path,
  });
}
