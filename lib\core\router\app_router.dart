import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Import providers
import '../../features/auth/providers/auth_provider.dart';

// Import screens
import '../../features/auth/screens/login_screen.dart';
import '../../features/dashboard/screens/dashboard_screen.dart';
// import '../../features/claim/screens/claim_list_screen.dart';
// import '../../features/claim/screens/claim_entry_screen.dart';
// import '../../features/claim/screens/claim_upload_screen.dart';
// import '../../features/doctor/screens/doctor_panel_screen.dart';
// import '../../features/consultation/screens/consultation_entry_screen.dart';
// import '../../features/tracking/screens/tracking_screen.dart';
// import '../../features/report/screens/report_screen.dart';
// import '../../features/admin/screens/admin_screen.dart';

/// Application router configuration
class AppRouter {
  static const String login = '/login';
  static const String dashboard = '/dashboard';
  static const String claimList = '/claims';
  static const String claimEntry = '/claims/entry';
  static const String claimUpload = '/claims/upload';
  static const String claimDetails = '/claims/:claimNo';
  static const String doctorPanel = '/doctor';
  static const String consultation = '/consultation';
  static const String tracking = '/tracking';
  static const String reports = '/reports';
  static const String admin = '/admin';
  static const String userManagement = '/admin/users';
  static const String doctorManagement = '/admin/doctors';

  /// Create router instance
  static GoRouter createRouter(WidgetRef ref) {
    return GoRouter(
      initialLocation: login,
      debugLogDiagnostics: true,
      redirect: (context, state) {
        final authState = ref.read(authProvider);
        final isLoggedIn = authState.isAuthenticated;
        final isLoggingIn = state.matchedLocation == login;

        if (!isLoggedIn && !isLoggingIn) {
          return login;
        }

        if (isLoggedIn && isLoggingIn) {
          return dashboard;
        }

        return null;
      },
      routes: [
        // ==================== AUTH ROUTES ====================
        GoRoute(
          path: login,
          name: 'login',
          builder: (context, state) => const LoginScreen(),
        ),

        // ==================== MAIN APP ROUTES ====================
        ShellRoute(
          builder: (context, state, child) {
            // TODO: Implement main app shell with navigation
            return Scaffold(
              body: child,
              // drawer: const AppDrawer(),
            );
          },
          routes: [
            // Dashboard
            GoRoute(
              path: dashboard,
              name: 'dashboard',
              builder: (context, state) => const DashboardScreen(),
            ),

            // Claim Management
            GoRoute(
              path: claimList,
              name: 'claimList',
              builder: (context, state) =>
                  const Placeholder(), // ClaimListScreen(),
              routes: [
                GoRoute(
                  path: 'entry',
                  name: 'claimEntry',
                  builder: (context, state) =>
                      const Placeholder(), // ClaimEntryScreen(),
                ),
                GoRoute(
                  path: 'upload',
                  name: 'claimUpload',
                  builder: (context, state) =>
                      const Placeholder(), // ClaimUploadScreen(),
                ),
              ],
            ),

            // Claim Details
            GoRoute(
              path: '/claims/:claimNo',
              name: 'claimDetails',
              builder: (context, state) {
                final claimNo = state.pathParameters['claimNo']!;
                return Placeholder(); // ClaimDetailsScreen(claimNo: claimNo),
              },
            ),

            // Doctor Panel
            GoRoute(
              path: doctorPanel,
              name: 'doctorPanel',
              builder: (context, state) =>
                  const Placeholder(), // DoctorPanelScreen(),
            ),

            // Consultation
            GoRoute(
              path: consultation,
              name: 'consultation',
              builder: (context, state) =>
                  const Placeholder(), // ConsultationEntryScreen(),
            ),

            // Tracking
            GoRoute(
              path: tracking,
              name: 'tracking',
              builder: (context, state) =>
                  const Placeholder(), // TrackingScreen(),
            ),

            // Reports
            GoRoute(
              path: reports,
              name: 'reports',
              builder: (context, state) =>
                  const Placeholder(), // ReportScreen(),
            ),

            // Admin Routes
            GoRoute(
              path: admin,
              name: 'admin',
              builder: (context, state) =>
                  const Placeholder(), // AdminScreen(),
              routes: [
                GoRoute(
                  path: 'users',
                  name: 'userManagement',
                  builder: (context, state) =>
                      const Placeholder(), // UserManagementScreen(),
                ),
                GoRoute(
                  path: 'doctors',
                  name: 'doctorManagement',
                  builder: (context, state) =>
                      const Placeholder(), // DoctorManagementScreen(),
                ),
              ],
            ),
          ],
        ),
      ],
      errorBuilder: (context, state) => ErrorScreen(error: state.error),
    );
  }

  /// Navigation helper methods
  static void goToLogin(BuildContext context) {
    context.go(login);
  }

  static void goToDashboard(BuildContext context) {
    context.go(dashboard);
  }

  static void goToClaimList(BuildContext context) {
    context.go(claimList);
  }

  static void goToClaimEntry(BuildContext context) {
    context.go(claimEntry);
  }

  static void goToClaimUpload(BuildContext context) {
    context.go(claimUpload);
  }

  static void goToClaimDetails(BuildContext context, String claimNo) {
    context.go('/claims/$claimNo');
  }

  static void goToDoctorPanel(BuildContext context) {
    context.go(doctorPanel);
  }

  static void goToConsultation(BuildContext context) {
    context.go(consultation);
  }

  static void goToTracking(BuildContext context) {
    context.go(tracking);
  }

  static void goToReports(BuildContext context) {
    context.go(reports);
  }

  static void goToAdmin(BuildContext context) {
    context.go(admin);
  }

  static void goToUserManagement(BuildContext context) {
    context.go(userManagement);
  }

  static void goToDoctorManagement(BuildContext context) {
    context.go(doctorManagement);
  }

  /// Check if current route requires admin access
  static bool requiresAdminAccess(String route) {
    return route.startsWith('/admin');
  }

  /// Check if current route requires doctor access
  static bool requiresDoctorAccess(String route) {
    return route == doctorPanel;
  }

  /// Get route title for app bar
  static String getRouteTitle(String route) {
    switch (route) {
      case login:
        return 'Login';
      case dashboard:
        return 'Dashboard';
      case claimList:
        return 'Claims';
      case claimEntry:
        return 'New Claim';
      case claimUpload:
        return 'Upload Claims';
      case doctorPanel:
        return 'Doctor Panel';
      case consultation:
        return 'Consultation';
      case tracking:
        return 'Tracking';
      case reports:
        return 'Reports';
      case admin:
        return 'Administration';
      case userManagement:
        return 'User Management';
      case doctorManagement:
        return 'Doctor Management';
      default:
        if (route.startsWith('/claims/')) {
          return 'Claim Details';
        }
        return 'Medical Bills ERP';
    }
  }
}

/// Error screen for router errors
class ErrorScreen extends StatelessWidget {
  final Exception? error;

  const ErrorScreen({super.key, this.error});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Error')),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            const Text(
              'Something went wrong',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Text(
              error?.toString() ?? 'Unknown error occurred',
              textAlign: TextAlign.center,
              style: const TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => AppRouter.goToDashboard(context),
              child: const Text('Go to Dashboard'),
            ),
          ],
        ),
      ),
    );
  }
}
