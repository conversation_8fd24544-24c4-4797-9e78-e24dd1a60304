/// Database schema definitions for Medical Bills ERP System
class DatabaseSchema {
  static const String databaseName = 'medical_bills.db';
  static const int databaseVersion = 1;

  // Table Names
  static const String usersTable = 'Users';
  static const String claimMasterTable = 'ClaimMaster';
  static const String medicalClaimRegisterTable = 'MedicalClaimRegister';
  static const String doctorsRegisterTable = 'DoctorsRegister';
  static const String consultationRegisterTable = 'ConsultationRegister';
  static const String claimStatusHistoryTable = 'ClaimStatusHistory';

  /// Users table - Stores login credentials and role info
  static const String createUsersTable = '''
    CREATE TABLE $usersTable (
      UserID TEXT PRIMARY KEY,
      Password TEXT NOT NULL,
      Role TEXT NOT NULL,
      Name TEXT,
      Email TEXT,
      Division TEXT,
      Subdivision TEXT,
      CreatedAt TEXT DEFAULT CURRENT_TIMESTAMP,
      UpdatedAt TEXT DEFAULT CURRENT_TIMESTAMP
    )
  ''';

  /// ClaimMaster table - Raw imported claims from Excel
  static const String createClaimMasterTable = '''
    CREATE TABLE $claimMasterTable (
      ClaimNo TEXT PRIMARY KEY,
      EmployeeNumber TEXT,
      Name TEXT,
      Designation TEXT,
      Organization TEXT,
      ClaimType TEXT,
      DateOfApplication TEXT,
      StartDateOfExpense TEXT,
      EndDateOfExpense TEXT,
      ClaimAmount REAL,
      SanctionedAmount REAL,
      DisallowedAmount REAL,
      Status TEXT DEFAULT 'Pending',
      PatientName TEXT,
      Relation TEXT,
      TreatmentType TEXT,
      CreatedAt TEXT DEFAULT CURRENT_TIMESTAMP,
      UpdatedAt TEXT DEFAULT CURRENT_TIMESTAMP
    )
  ''';

  /// MedicalClaimRegister table - Working table for verified and processed claims
  static const String createMedicalClaimRegisterTable = '''
    CREATE TABLE $medicalClaimRegisterTable (
      ClaimNo TEXT PRIMARY KEY,
      
      -- Basic Claim Info (copied from ClaimMaster)
      EmployeeNumber TEXT,
      Name TEXT,
      Designation TEXT,
      Organization TEXT,
      ClaimType TEXT,
      DateOfApplication TEXT,
      StartDateOfExpense TEXT,
      EndDateOfExpense TEXT,
      ClaimAmount REAL,
      PatientName TEXT,
      Relation TEXT,
      TreatmentType TEXT,
      
      -- Workflow / Processing Fields
      SanctionedAmount REAL,
      DisallowedAmount REAL,
      DiseaseName TEXT,
      DrName TEXT,
      HospitalStay TEXT,
      PHStatus TEXT DEFAULT 'SDN',
      Status TEXT DEFAULT 'Pending',
      ReturnReason TEXT,
      DoctorRemarks TEXT,
      ClaimReceivedDate TEXT,
      ForwardedBy TEXT,
      ReceivedBy TEXT,
      ForwardedDate TEXT,
      ReturnedBy TEXT,
      
      -- Payment Section
      PaidSalaryMonth TEXT,
      DrVoucherNo TEXT,
      DrAmount REAL,
      
      CreatedAt TEXT DEFAULT CURRENT_TIMESTAMP,
      UpdatedAt TEXT DEFAULT CURRENT_TIMESTAMP,
      
      FOREIGN KEY (ClaimNo) REFERENCES $claimMasterTable(ClaimNo)
    )
  ''';

  /// DoctorsRegister table - List of doctors available for verification
  static const String createDoctorsRegisterTable = '''
    CREATE TABLE $doctorsRegisterTable (
      DrID INTEGER PRIMARY KEY AUTOINCREMENT,
      Name TEXT NOT NULL,
      Degree TEXT,
      Address TEXT,
      MobileNo TEXT,
      Email TEXT,
      IsActive INTEGER DEFAULT 1,
      CreatedAt TEXT DEFAULT CURRENT_TIMESTAMP,
      UpdatedAt TEXT DEFAULT CURRENT_TIMESTAMP
    )
  ''';

  /// ConsultationRegister table - Tracks consultations and reconciliations
  static const String createConsultationRegisterTable = '''
    CREATE TABLE $consultationRegisterTable (
      ID INTEGER PRIMARY KEY AUTOINCREMENT,
      ClaimNo TEXT NOT NULL,
      ConsultationDate TEXT,
      ConsultationAmount REAL,
      ReconciliationDate TEXT,
      ReconciliationAmount REAL,
      CreatedAt TEXT DEFAULT CURRENT_TIMESTAMP,
      UpdatedAt TEXT DEFAULT CURRENT_TIMESTAMP,
      
      FOREIGN KEY (ClaimNo) REFERENCES $medicalClaimRegisterTable(ClaimNo)
    )
  ''';

  /// ClaimStatusHistory table - Tracks the journey of a bill through offices
  static const String createClaimStatusHistoryTable = '''
    CREATE TABLE $claimStatusHistoryTable (
      ID INTEGER PRIMARY KEY AUTOINCREMENT,
      ClaimNo TEXT NOT NULL,
      Action TEXT NOT NULL,
      FromUser TEXT,
      ToUser TEXT,
      ActionDate TEXT DEFAULT CURRENT_TIMESTAMP,
      Remarks TEXT,
      
      FOREIGN KEY (ClaimNo) REFERENCES $medicalClaimRegisterTable(ClaimNo)
    )
  ''';

  /// List of all table creation statements
  static const List<String> createTableStatements = [
    createUsersTable,
    createClaimMasterTable,
    createMedicalClaimRegisterTable,
    createDoctorsRegisterTable,
    createConsultationRegisterTable,
    createClaimStatusHistoryTable,
  ];

  /// Insert default admin user
  static const String insertDefaultAdmin = '''
    INSERT OR IGNORE INTO $usersTable (UserID, Password, Role, Name, Email, Division)
    VALUES ('admin', 'admin123', 'Admin', 'System Administrator', '<EMAIL>', 'IT')
  ''';

  /// Insert sample doctors
  static const String insertSampleDoctors = '''
    INSERT OR IGNORE INTO $doctorsRegisterTable (Name, Degree, Address, MobileNo, Email)
    VALUES 
    ('Dr. Rajesh Patel', 'MBBS, MD', 'Amreli Civil Hospital', '**********', '<EMAIL>'),
    ('Dr. Priya Shah', 'MBBS, MS', 'District Hospital', '**********', '<EMAIL>'),
    ('Dr. Amit Kumar', 'MBBS, DM', 'Private Clinic', '**********', '<EMAIL>'),
    ('Dr. Sunita Joshi', 'MBBS, DNB', 'Specialty Center', '**********', '<EMAIL>'),
    ('Dr. Vikram Singh', 'MBBS, MCh', 'Super Specialty Hospital', '**********', '<EMAIL>')
  ''';

  /// List of initial data insertion statements
  static const List<String> initialDataStatements = [
    insertDefaultAdmin,
    insertSampleDoctors,
  ];
}
